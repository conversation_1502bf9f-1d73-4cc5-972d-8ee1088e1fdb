<?php

namespace App\Http\Controllers\Medical;

use App\Http\Controllers\Controller;
use App\Quote;
use App\QuoteItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Webkul\Product\Repositories\ProductRepository;
use Illuminate\Foundation\Validation\ValidatesRequests;
use App\QuoteRequest;
use App\QuoteRequestItem;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Medical\ProductController;
use Webkul\Product\Models\Product;

class QuoteController extends Controller
{
    use ValidatesRequests;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected ProductRepository $productRepository,
        protected ProductController $productController
    ) {}

    /**
     * Hiển thị trang báo giá
     */
    public function index()
    {
        $customer = Auth::guard('customer')->user();

        if (!$customer) {
            return redirect('/signin');
        }

        $quote = Quote::where('customer_id', $customer->id)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$quote) {
            $quote = new Quote();
            $quote->customer_id = $customer->id;
            $quote->customer_email = $customer->email;
            $quote->customer_name = $customer->first_name . ' ' . $customer->last_name;
            $quote->customer_phone = $customer->phone;
            $quote->status = 'pending';
            $quote->save();
        }

        // Lấy recently viewed products (5 sản phẩm)
        $recentlyViewedProducts = collect();

        if ($customer) {
            // User đã đăng nhập - lấy từ database
            $recentlyViewedRecords = \App\Models\RecentlyViewedProduct::with([
                'product.images',
                'product.categories',
                'product.attribute_values',
                'product.product_flats',
                'product.variants'
            ])->forCustomer($customer->id)->recent(5)->get();
            $products = $recentlyViewedRecords->pluck('product')->filter();
        } else {
            // Guest user - lấy từ session
            $recentlyViewedIds = session('recently_viewed_products', []);

            if (!empty($recentlyViewedIds)) {
                $productIds = array_slice($recentlyViewedIds, 0, 5);
                $productsFromDb = Product::with(['images', 'categories', 'attribute_values', 'product_flats', 'variants'])
                    ->whereIn('id', $productIds)
                    ->get();

                // Sắp xếp theo thứ tự trong session (mới nhất trước)
                $products = collect($recentlyViewedIds)->map(function ($id) use ($productsFromDb) {
                    return $productsFromDb->firstWhere('id', $id);
                })->filter()->take(5);
            } else {
                $products = collect();
            }
        }

        // Xử lý dữ liệu sản phẩm giống như HomeController và OurProductController
        if ($products->isNotEmpty()) {
            $recentlyViewedProducts = $this->productController->processProductData($products);
        }

        return view('medical::quote.index', [
            'quote' => $quote,
            'quoteItems' => $quote->items()->with('product')->get(),
            'quoteCount' => $quote->items()->count(),
            'recentlyViewedProducts' => $recentlyViewedProducts
        ]);
    }

    /**
     * Thêm sản phẩm vào báo giá
     */
    public function store(Request $request): JsonResponse
    {
        $this->validate($request, [
            'product_id' => 'required|integer|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'unit_type' => 'required|integer'
        ]);

        try {
            $customer = Auth::guard('customer')->user();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để thêm sản phẩm vào báo giá'
                ], 401);
            }

            $product = $this->productRepository->find($request->product_id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy sản phẩm'
                ], 404);
            }

            // Tìm hoặc tạo quote cho khách hàng
            $quote = Quote::where('customer_id', $customer->id)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$quote) {
                $quote = new Quote();
                $quote->customer_id = $customer->id;
                $quote->customer_email = $customer->email;
                $quote->customer_name = $customer->first_name . ' ' . $customer->last_name;
                $quote->customer_phone = $customer->phone;
                $quote->status = 'pending';
                $quote->save();
            }

            // Kiểm tra xem sản phẩm đã có trong báo giá chưa
            $quoteItem = QuoteItem::where('quote_id', $quote->id)
                ->where('product_id', $product->id)
                ->first();

            if ($quoteItem) {
                // Nếu đã có, cập nhật số lượng
                $quoteItem->quantity += $request->quantity;
                $quoteItem->save();
            } else {
                // Nếu chưa có, tạo mới
                $quoteItem = new QuoteItem();
                $quoteItem->quote_id = $quote->id;
                $quoteItem->product_id = $product->id;
                $quoteItem->product_type = $product->type;
                $quoteItem->name = $product->name;
                $quoteItem->quantity = $request->quantity;
                $quoteItem->additional = [
                    'product' => $product->toArray(),
                    'unit_type' => $request->unit_type
                ];
                $quoteItem->save();
            }

            // Kiểm tra inventory
            $isOutOfStock = false;
            if ($product->manage_stock && $product->totalQuantity() < $request->quantity) {
                $isOutOfStock = true;
            }

            // Lấy số lượng sản phẩm trong báo giá
            $quoteCount = $quote->items()->count();

            $message = $isOutOfStock
                ? 'Sản phẩm tạm thời hết hàng. Yêu cầu báo giá của bạn sẽ được xử lý. Vui lòng liên hệ hotline để biết thêm chi tiết.'
                : 'Đã thêm sản phẩm vào báo giá';

            return response()->json([
                'success' => true,
                'message' => $message,
                'quote_count' => $quoteCount,
                'is_out_of_stock' => $isOutOfStock
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể thêm sản phẩm vào báo giá. Vui lòng thử lại sau.',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }

    /**
     * Cập nhật số lượng sản phẩm trong báo giá
     */
    public function updateQuantity(Request $request): JsonResponse
    {
        $this->validate($request, [
            'item_id' => 'required|integer',
            'quantity' => 'required|integer|min:1'
        ]);

        try {
            $customer = Auth::guard('customer')->user();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để cập nhật báo giá'
                ], 401);
            }

            $quoteItem = QuoteItem::find($request->item_id);

            if (!$quoteItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy sản phẩm trong báo giá'
                ], 404);
            }

            $quote = Quote::find($quoteItem->quote_id);

            if (!$quote || $quote->customer_id != $customer->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền cập nhật báo giá này'
                ], 403);
            }

            $quoteItem->quantity = $request->quantity;
            $quoteItem->save();

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật số lượng thành công!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể cập nhật số lượng. Vui lòng thử lại sau.',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }

    /**
     * Xóa sản phẩm khỏi báo giá
     */
    public function destroy(Request $request): JsonResponse
    {
        $this->validate($request, [
            'item_id' => 'required|integer'
        ]);

        try {
            DB::beginTransaction();

            $customer = Auth::guard('customer')->user();

            if (!$customer) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để xóa sản phẩm khỏi báo giá'
                ], 401);
            }

            $quoteItem = QuoteItem::find($request->item_id);

            if (!$quoteItem) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy sản phẩm trong báo giá'
                ], 200);
            }

            $quote = Quote::find($quoteItem->quote_id);

            if (!$quote || $quote->customer_id != $customer->id) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền xóa sản phẩm khỏi báo giá này'
                ], 200);
            }

            $quoteItem->delete();

            // Lấy số lượng sản phẩm trong báo giá
            $quoteCount = $quote->items()->count();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Xóa sản phẩm khỏi báo giá thành công!',
                'quote_count' => $quoteCount
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa sản phẩm khỏi báo giá. Vui lòng thử lại sau.',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 200);
        }
    }

    /**
     * Gửi yêu cầu báo giá
     */
    public function submit(Request $request): JsonResponse
    {
        $this->validate($request, [
            'selected_items' => 'required|array',
            'selected_items.*' => 'required|integer|exists:quote_items,id',
            'note' => 'nullable|string|max:1000'
        ]);

        try {
            $customer = Auth::guard('customer')->user();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để gửi yêu cầu báo giá'
                ], 401);
            }

            $quote = Quote::where('customer_id', $customer->id)
                ->where('status', 'pending')
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$quote) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy báo giá'
                ], 404);
            }

            // Lấy các items được chọn
            $selectedItems = QuoteItem::whereIn('id', $request->selected_items)
                ->where('quote_id', $quote->id)
                ->get();

            if ($selectedItems->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng chọn ít nhất một sản phẩm để gửi yêu cầu báo giá'
                ], 400);
            }

            // Tạo quote request mới
            $quoteRequest = new QuoteRequest();
            $quoteRequest->customer_id = $customer->id;
            $quoteRequest->customer_email = $customer->email;
            $quoteRequest->customer_name = $customer->first_name . ' ' . $customer->last_name;
            $quoteRequest->customer_phone = $customer->phone;
            $quoteRequest->customer_note = $request->note;
            $quoteRequest->status = 'pending';
            $quoteRequest->save();

            // Copy các items được chọn sang quote request
            foreach ($selectedItems as $item) {
                $quoteRequestItem = new QuoteRequestItem();
                $quoteRequestItem->quote_request_id = $quoteRequest->id;
                $quoteRequestItem->product_id = $item->product_id;
                $quoteRequestItem->product_type = $item->product_type;
                $quoteRequestItem->name = $item->name;
                $quoteRequestItem->quantity = $item->quantity;
                $quoteRequestItem->additional = $item->additional;
                $quoteRequestItem->save();

                // Xóa item khỏi quote cũ
                $item->delete();
            }

            return response()->json([
                'success' => true,
                'message' => 'Gửi báo giá thành công!',
                'redirect_url' => '/profile/cost_detail/' . $quoteRequest->id
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể gửi yêu cầu báo giá. Vui lòng thử lại sau.',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }

    /**
     * Lấy số lượng sản phẩm trong báo giá
     */
    public function count(): JsonResponse
    {
        try {
            $customer = Auth::guard('customer')->user();

            if (!$customer) {
                return response()->json([
                    'success' => true,
                    'quote_count' => 0
                ]);
            }

            $quote = Quote::where('customer_id', $customer->id)
                ->where('status', 'pending')
                ->orderBy('created_at', 'desc')
                ->first();

            $quoteCount = $quote ? $quote->items()->count() : 0;

            return response()->json([
                'success' => true,
                'quote_count' => $quoteCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy số lượng sản phẩm trong báo giá.',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ],
                'quote_count' => 0
            ], 500);
        }
    }
}
