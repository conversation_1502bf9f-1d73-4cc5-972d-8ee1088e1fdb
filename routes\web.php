<?php

use App\Http\Controllers\Medical\HomeController;
use App\Http\Controllers\Medical\ProductDetailController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Medical\SigninController;
use App\Http\Controllers\Medical\SignupController;
use App\Http\Controllers\Medical\ForgotPasswordController;
use App\Http\Controllers\Medical\ResetPasswordController;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Medical\CartController;
use App\Http\Controllers\Medical\CheckoutController;
use App\Http\Controllers\Medical\ProfileController;
use App\Http\Controllers\Medical\CostController;
use App\Http\Controllers\Medical\QuickOrderController;
use App\Http\Controllers\Medical\NewsController;
use App\Http\Controllers\Medical\NewsDetailController;
use App\Http\Controllers\Medical\OrderDetailController;
use App\Http\Controllers\Medical\CostDetailController;
use App\Http\Controllers\Medical\GuideController;
use App\Http\Controllers\Medical\RegisterPartnerController;
use App\Http\Controllers\Medical\QuoteController;
use App\Http\Controllers\Medical\OurProductController;
use App\Http\Controllers\Medical\ActiveIngredientController;
use App\Http\Controllers\Medical\IngredientDetailController;
use App\Http\Controllers\Medical\BrandsController;
use App\Http\Controllers\Medical\BrandsDetailController;
use App\Http\Controllers\Medical\IllnessController;
use App\Http\Controllers\Medical\IllnessCategoryController;
use App\Http\Controllers\Medical\IllnessDetailController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_middleware'),
    'verified'
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});

Route::get('/signin', [SigninController::class, 'index'])->name('signin');
Route::post('/signin', [SigninController::class, 'signin'])->name('signin.submit');

// Forgot Password Routes
Route::get('/forgot-password', [ForgotPasswordController::class, 'create'])->name('medical.forgot_password.create');
Route::post('/forgot-password', [ForgotPasswordController::class, 'store'])->name('medical.forgot_password.store');

// Reset Password Routes
Route::get('/reset-password/{token}', [ResetPasswordController::class, 'create'])->name('medical.reset_password.create');
Route::post('/reset-password', [ResetPasswordController::class, 'store'])->name('medical.reset_password.store');

Route::get('/signup', [SignupController::class, 'index'])->name('signup');
Route::post('/signup', [SignupController::class, 'signup'])->name('signup.submit');

Route::get('/', [HomeController::class, 'index'])->name('shop.home.index');
Route::get('/home', [HomeController::class, 'index'])->name('home');

Route::get('/product/detail', [ProductDetailController::class, 'index'])->name('product_detail');

Route::get('/cart', [CartController::class, 'index'])->name('cart');

Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout');
Route::post('/checkout/place-order', [CheckoutController::class, 'placeOrder'])->name('checkout.placeOrder');
Route::post('/checkout/create-new-address', [CheckoutController::class, 'createNewAddress'])->name('checkout.create-new-address');
Route::post('/checkout/delete-address', [CheckoutController::class, 'deleteAddress'])->name('checkout.delete-address');
Route::post('/buy-now', [CheckoutController::class, 'buyNow'])->name('buy.now');

Route::get('/cost', [CostController::class, 'index'])->name('cost');

Route::get('/quickorder', [QuickOrderController::class, 'index'])->name('quick_order');

Route::get('/profile/{tab?}', [ProfileController::class, 'index'])->name('profile');
Route::post('/update-profile', [ProfileController::class, 'update'])->name('profile.update');

Route::get('/news', [NewsController::class, 'index'])->name('news');
Route::get('/news/category/{slug}', [NewsController::class, 'category'])->name('news.category');
Route::get('/news/{slug}', [NewsDetailController::class, 'index'])->name('news.detail');

Route::get('/order_detail/{id}', [OrderDetailController::class, 'index'])->name('order_detail');

Route::get('/cost_detail/{id}', [CostDetailController::class, 'index'])->name('cost_detail');
Route::get('/profile/cost_detail/{id}', [CostDetailController::class, 'profileCostDetail'])->name('profile.cost_detail');

Route::get('/guide', [GuideController::class, 'index'])->name('guide');


Route::get('/register_partner', [RegisterPartnerController::class, 'index'])->name('register_partner');

Route::get('/product', [OurProductController::class, 'index'])->name('our_product');

Route::get('/recently-viewed', [OurProductController::class, 'recentlyViewed'])->name('recently_viewed');

Route::get('/activeingredient', [ActiveIngredientController::class, 'index'])->name('active_ingredient');

Route::get('/ingredient_detail/{slug}', [ActiveIngredientController::class, 'show'])->name('ingredient_detail');

Route::get('/brands', [BrandsController::class, 'index'])->name('brands');

Route::get('/brands_detail/{id}', [BrandsDetailController::class, 'show'])->name('brands_detail');
Route::get('/brands_detail', [BrandsDetailController::class, 'index'])->name('brands_detail.index');

Route::get('/illness', [IllnessController::class, 'index'])->name('medical.illness');

Route::get('/illness_category', [IllnessCategoryController::class, 'index'])->name('medical.illness_category');
Route::get('/illness_category/{slug}', [IllnessCategoryController::class, 'index'])->name('medical.illness_category.show');

Route::get('/illness_detail', [IllnessDetailController::class, 'index'])->name('illness_detail');
Route::get('/illness_detail/{slug}', [IllnessDetailController::class, 'show'])->name('medical.illness_detail');

// API routes for AJAX
Route::get('/api/illness-category/{id}/illnesses', [IllnessController::class, 'getIllnessesByCategory'])->name('api.illness.category.illnesses');

// Search API routes
Route::prefix('api/search')->group(function () {
    Route::get('/suggestions', [App\Http\Controllers\Medical\Api\SearchController::class, 'suggestions'])->name('api.search.suggestions');
    Route::post('/clear-history', [App\Http\Controllers\Medical\Api\SearchController::class, 'clearHistory'])->name('api.search.clear-history');
});

Route::post('/logout', function() {
    Auth::guard('customer')->logout();
    return redirect('/');
})->name('logout');

// Medical Cart API Routes
Route::prefix('api/medical')->group(function () {
    Route::post('/cart', [App\Http\Controllers\Medical\CartController::class, 'store'])
        ->name('medical.cart.store');
    Route::post('/cart/update-quantity', [App\Http\Controllers\Medical\CartController::class, 'updateQuantity'])
        ->name('cart.update-quantity');
    Route::post('/cart/sync-quantities', [App\Http\Controllers\Medical\CartController::class, 'syncQuantities'])
        ->name('cart.sync-quantities');
});

// Cart routes
Route::prefix('api/medical/cart')->group(function () {
    Route::post('update-quantity', [CartController::class, 'updateQuantity'])->name('cart.update-quantity');
    Route::post('sync-quantities', [CartController::class, 'syncQuantities'])->name('cart.sync-quantities');
    Route::post('remove-item', [CartController::class, 'removeItem'])->name('cart.remove-item');
    Route::post('remove-items', [CartController::class, 'removeItems'])->name('cart.remove-items');
});

// Quote routes
Route::get('/quote', [QuoteController::class, 'index'])->name('quote');

// Quote API routes
Route::prefix('api/medical/quote')->group(function () {
    Route::post('/', [QuoteController::class, 'store'])->name('quote.store');
    Route::post('/update-quantity', [QuoteController::class, 'updateQuantity'])->name('quote.update-quantity');
    Route::post('/remove-item', [QuoteController::class, 'destroy'])->name('quote.remove-item');
    Route::post('/submit', [QuoteController::class, 'submit'])->name('quote.submit');
    Route::get('/count', [QuoteController::class, 'count'])->name('quote.count');
});

// Product Questions API routes
Route::prefix('api/medical')->group(function () {
    Route::post('/product-questions', [App\Http\Controllers\Medical\Api\ProductQuestionController::class, 'store'])
        ->name('medical.product-questions.store');
    Route::get('/product-questions', [App\Http\Controllers\Medical\Api\ProductQuestionController::class, 'index'])
        ->name('medical.product-questions.index');
});

// Debug route để kiểm tra session
Route::get('/debug/session', function () {
    return response()->json([
        'recently_viewed_products' => session('recently_viewed_products', []),
        'all_session_data' => session()->all()
    ]);
});

// Medical Cart API - Add to Cart
Route::post('/api/medical/cart/add', [App\Http\Controllers\Medical\CartController::class, 'addToCart'])
    ->name('medical.cart.add');