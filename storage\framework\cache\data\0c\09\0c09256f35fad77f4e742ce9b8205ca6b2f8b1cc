1754041271a:3:{i:0;a:3:{s:3:"key";s:4:"cart";s:5:"label";s:25:"Thuộc tính giỏ hàng";s:8:"children";a:7:{i:0;a:3:{s:3:"key";s:19:"cart|base_sub_total";s:4:"type";s:5:"price";s:5:"label";s:12:"Tổng phụ";}i:1;a:3:{s:3:"key";s:14:"cart|items_qty";s:4:"type";s:7:"integer";s:5:"label";s:32:"Tổng số lượng mặt hàng";}i:2;a:4:{s:3:"key";s:19:"cart|payment_method";s:4:"type";s:6:"select";s:7:"options";a:4:{i:0;a:2:{s:2:"id";s:19:"paypal_smart_button";s:10:"admin_name";s:19:"PayPal Smart Button";}i:1;a:2:{s:2:"id";s:15:"paypal_standard";s:10:"admin_name";s:15:"PayPal Standard";}i:2;a:2:{s:2:"id";s:14:"cashondelivery";s:10:"admin_name";s:16:"Cash On Delivery";}i:3;a:2:{s:2:"id";s:13:"moneytransfer";s:10:"admin_name";s:14:"Money Transfer";}}s:5:"label";s:27:"Phương thức thanh toán";}i:3;a:4:{s:3:"key";s:20:"cart|shipping_method";s:4:"type";s:6:"select";s:7:"options";a:2:{i:0;a:2:{s:2:"id";s:8:"flatrate";s:10:"admin_name";s:9:"Flat Rate";}i:1;a:2:{s:2:"id";s:4:"free";s:10:"admin_name";s:13:"Free Shipping";}}s:5:"label";s:30:"Phương thức vận chuyển";}i:4;a:3:{s:3:"key";s:13:"cart|postcode";s:4:"type";s:4:"text";s:5:"label";s:31:"Mã bưu điện vận chuyển";}i:5;a:4:{s:3:"key";s:10:"cart|state";s:4:"type";s:6:"select";s:7:"options";a:16:{i:0;a:3:{s:2:"id";s:2:"AT";s:10:"admin_name";s:7:"Austria";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:9:{i:94;O:8:"stdClass":5:{s:2:"id";i:95;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"WI";s:12:"default_name";s:4:"Wien";}i:95;O:8:"stdClass":5:{s:2:"id";i:96;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"NO";s:12:"default_name";s:17:"Niederösterreich";}i:96;O:8:"stdClass":5:{s:2:"id";i:97;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"OO";s:12:"default_name";s:15:"Oberösterreich";}i:97;O:8:"stdClass":5:{s:2:"id";i:98;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"SB";s:12:"default_name";s:8:"Salzburg";}i:98;O:8:"stdClass":5:{s:2:"id";i:99;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"KN";s:12:"default_name";s:8:"Kärnten";}i:99;O:8:"stdClass":5:{s:2:"id";i:100;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"ST";s:12:"default_name";s:10:"Steiermark";}i:100;O:8:"stdClass":5:{s:2:"id";i:101;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"TI";s:12:"default_name";s:5:"Tirol";}i:101;O:8:"stdClass":5:{s:2:"id";i:102;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"BL";s:12:"default_name";s:10:"Burgenland";}i:102;O:8:"stdClass":5:{s:2:"id";i:103;s:10:"country_id";i:16;s:12:"country_code";s:2:"AT";s:4:"code";s:2:"VB";s:12:"default_name";s:10:"Vorarlberg";}}s:28:" * escapeWhenCastingToString";b:0;}}i:1;a:3:{s:2:"id";s:2:"BR";s:10:"admin_name";s:6:"Brazil";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:27:{i:484;O:8:"stdClass":5:{s:2:"id";i:485;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"AC";s:12:"default_name";s:4:"Acre";}i:485;O:8:"stdClass":5:{s:2:"id";i:486;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"AL";s:12:"default_name";s:7:"Alagoas";}i:486;O:8:"stdClass":5:{s:2:"id";i:487;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"AP";s:12:"default_name";s:6:"Amapá";}i:487;O:8:"stdClass":5:{s:2:"id";i:488;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"AM";s:12:"default_name";s:8:"Amazonas";}i:488;O:8:"stdClass":5:{s:2:"id";i:489;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"BA";s:12:"default_name";s:5:"Bahia";}i:489;O:8:"stdClass":5:{s:2:"id";i:490;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"CE";s:12:"default_name";s:6:"Ceará";}i:490;O:8:"stdClass":5:{s:2:"id";i:491;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"ES";s:12:"default_name";s:15:"Espírito Santo";}i:491;O:8:"stdClass":5:{s:2:"id";i:492;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"GO";s:12:"default_name";s:6:"Goiás";}i:492;O:8:"stdClass":5:{s:2:"id";i:493;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"MA";s:12:"default_name";s:9:"Maranhão";}i:493;O:8:"stdClass":5:{s:2:"id";i:494;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"MT";s:12:"default_name";s:11:"Mato Grosso";}i:494;O:8:"stdClass":5:{s:2:"id";i:495;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"MS";s:12:"default_name";s:18:"Mato Grosso do Sul";}i:495;O:8:"stdClass":5:{s:2:"id";i:496;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"MG";s:12:"default_name";s:12:"Minas Gerais";}i:496;O:8:"stdClass":5:{s:2:"id";i:497;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"PA";s:12:"default_name";s:5:"Pará";}i:497;O:8:"stdClass":5:{s:2:"id";i:498;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"PB";s:12:"default_name";s:8:"Paraíba";}i:498;O:8:"stdClass":5:{s:2:"id";i:499;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"PR";s:12:"default_name";s:7:"Paraná";}i:499;O:8:"stdClass":5:{s:2:"id";i:500;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"PE";s:12:"default_name";s:10:"Pernambuco";}i:500;O:8:"stdClass":5:{s:2:"id";i:501;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"PI";s:12:"default_name";s:6:"Piauí";}i:501;O:8:"stdClass":5:{s:2:"id";i:502;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"RJ";s:12:"default_name";s:14:"Rio de Janeiro";}i:502;O:8:"stdClass":5:{s:2:"id";i:503;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"RN";s:12:"default_name";s:19:"Rio Grande do Norte";}i:503;O:8:"stdClass":5:{s:2:"id";i:504;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"RS";s:12:"default_name";s:17:"Rio Grande do Sul";}i:504;O:8:"stdClass":5:{s:2:"id";i:505;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"RO";s:12:"default_name";s:9:"Rondônia";}i:505;O:8:"stdClass":5:{s:2:"id";i:506;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"RR";s:12:"default_name";s:7:"Roraima";}i:506;O:8:"stdClass":5:{s:2:"id";i:507;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"SC";s:12:"default_name";s:14:"Santa Catarina";}i:507;O:8:"stdClass":5:{s:2:"id";i:508;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"SP";s:12:"default_name";s:10:"São Paulo";}i:508;O:8:"stdClass":5:{s:2:"id";i:509;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"SE";s:12:"default_name";s:7:"Sergipe";}i:509;O:8:"stdClass":5:{s:2:"id";i:510;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"TO";s:12:"default_name";s:9:"Tocantins";}i:510;O:8:"stdClass":5:{s:2:"id";i:511;s:10:"country_id";i:31;s:12:"country_code";s:2:"BR";s:4:"code";s:2:"DF";s:12:"default_name";s:16:"Distrito Federal";}}s:28:" * escapeWhenCastingToString";b:0;}}i:2;a:3:{s:2:"id";s:2:"CA";s:10:"admin_name";s:6:"Canada";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:13:{i:65;O:8:"stdClass":5:{s:2:"id";i:66;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"AB";s:12:"default_name";s:7:"Alberta";}i:66;O:8:"stdClass":5:{s:2:"id";i:67;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"BC";s:12:"default_name";s:16:"British Columbia";}i:67;O:8:"stdClass":5:{s:2:"id";i:68;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"MB";s:12:"default_name";s:8:"Manitoba";}i:68;O:8:"stdClass":5:{s:2:"id";i:69;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"NL";s:12:"default_name";s:25:"Newfoundland and Labrador";}i:69;O:8:"stdClass":5:{s:2:"id";i:70;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"NB";s:12:"default_name";s:13:"New Brunswick";}i:70;O:8:"stdClass":5:{s:2:"id";i:71;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"NS";s:12:"default_name";s:11:"Nova Scotia";}i:71;O:8:"stdClass":5:{s:2:"id";i:72;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"NT";s:12:"default_name";s:21:"Northwest Territories";}i:72;O:8:"stdClass":5:{s:2:"id";i:73;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"NU";s:12:"default_name";s:7:"Nunavut";}i:73;O:8:"stdClass":5:{s:2:"id";i:74;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"ON";s:12:"default_name";s:7:"Ontario";}i:74;O:8:"stdClass":5:{s:2:"id";i:75;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"PE";s:12:"default_name";s:20:"Prince Edward Island";}i:75;O:8:"stdClass":5:{s:2:"id";i:76;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"QC";s:12:"default_name";s:6:"Quebec";}i:76;O:8:"stdClass":5:{s:2:"id";i:77;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"SK";s:12:"default_name";s:12:"Saskatchewan";}i:77;O:8:"stdClass":5:{s:2:"id";i:78;s:10:"country_id";i:40;s:12:"country_code";s:2:"CA";s:4:"code";s:2:"YT";s:12:"default_name";s:15:"Yukon Territory";}}s:28:" * escapeWhenCastingToString";b:0;}}i:3;a:3:{s:2:"id";s:2:"HR";s:10:"admin_name";s:7:"Croatia";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:21:{i:511;O:8:"stdClass":5:{s:2:"id";i:512;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-01";s:12:"default_name";s:21:"Zagrebačka županija";}i:512;O:8:"stdClass":5:{s:2:"id";i:513;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-02";s:12:"default_name";s:28:"Krapinsko-zagorska županija";}i:513;O:8:"stdClass":5:{s:2:"id";i:514;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-03";s:12:"default_name";s:30:"Sisačko-moslavačka županija";}i:514;O:8:"stdClass":5:{s:2:"id";i:515;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-04";s:12:"default_name";s:21:"Karlovačka županija";}i:515;O:8:"stdClass":5:{s:2:"id";i:516;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-05";s:12:"default_name";s:22:"Varaždinska županija";}i:516;O:8:"stdClass":5:{s:2:"id";i:517;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-06";s:12:"default_name";s:35:"Koprivničko-križevačka županija";}i:517;O:8:"stdClass":5:{s:2:"id";i:518;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-07";s:12:"default_name";s:32:"Bjelovarsko-bilogorska županija";}i:518;O:8:"stdClass":5:{s:2:"id";i:519;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-08";s:12:"default_name";s:28:"Primorsko-goranska županija";}i:519;O:8:"stdClass":5:{s:2:"id";i:520;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-09";s:12:"default_name";s:24:"Ličko-senjska županija";}i:520;O:8:"stdClass":5:{s:2:"id";i:521;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-10";s:12:"default_name";s:32:"Virovitičko-podravska županija";}i:521;O:8:"stdClass":5:{s:2:"id";i:522;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-11";s:12:"default_name";s:29:"Požeško-slavonska županija";}i:522;O:8:"stdClass":5:{s:2:"id";i:523;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-12";s:12:"default_name";s:26:"Brodsko-posavska županija";}i:523;O:8:"stdClass":5:{s:2:"id";i:524;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-13";s:12:"default_name";s:18:"Zadarska županija";}i:524;O:8:"stdClass":5:{s:2:"id";i:525;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-14";s:12:"default_name";s:28:"Osječko-baranjska županija";}i:525;O:8:"stdClass":5:{s:2:"id";i:526;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-15";s:12:"default_name";s:27:"Šibensko-kninska županija";}i:526;O:8:"stdClass":5:{s:2:"id";i:527;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-16";s:12:"default_name";s:30:"Vukovarsko-srijemska županija";}i:527;O:8:"stdClass":5:{s:2:"id";i:528;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-17";s:12:"default_name";s:30:"Splitsko-dalmatinska županija";}i:528;O:8:"stdClass":5:{s:2:"id";i:529;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-18";s:12:"default_name";s:18:"Istarska županija";}i:529;O:8:"stdClass":5:{s:2:"id";i:530;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-19";s:12:"default_name";s:33:"Dubrovačko-neretvanska županija";}i:530;O:8:"stdClass":5:{s:2:"id";i:531;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-20";s:12:"default_name";s:21:"Međimurska županija";}i:531;O:8:"stdClass":5:{s:2:"id";i:532;s:10:"country_id";i:59;s:12:"country_code";s:2:"HR";s:4:"code";s:5:"HR-21";s:12:"default_name";s:11:"Grad Zagreb";}}s:28:" * escapeWhenCastingToString";b:0;}}i:4;a:3:{s:2:"id";s:2:"EE";s:10:"admin_name";s:7:"Estonia";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:15:{i:339;O:8:"stdClass":5:{s:2:"id";i:340;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-37";s:12:"default_name";s:8:"Harjumaa";}i:340;O:8:"stdClass":5:{s:2:"id";i:341;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-39";s:12:"default_name";s:7:"Hiiumaa";}i:341;O:8:"stdClass":5:{s:2:"id";i:342;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-44";s:12:"default_name";s:11:"Ida-Virumaa";}i:342;O:8:"stdClass":5:{s:2:"id";i:343;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-49";s:12:"default_name";s:10:"Jõgevamaa";}i:343;O:8:"stdClass":5:{s:2:"id";i:344;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-51";s:12:"default_name";s:9:"Järvamaa";}i:344;O:8:"stdClass":5:{s:2:"id";i:345;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-57";s:12:"default_name";s:10:"Läänemaa";}i:345;O:8:"stdClass":5:{s:2:"id";i:346;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-59";s:12:"default_name";s:15:"Lääne-Virumaa";}i:346;O:8:"stdClass":5:{s:2:"id";i:347;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-65";s:12:"default_name";s:9:"Põlvamaa";}i:347;O:8:"stdClass":5:{s:2:"id";i:348;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-67";s:12:"default_name";s:9:"Pärnumaa";}i:348;O:8:"stdClass":5:{s:2:"id";i:349;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-70";s:12:"default_name";s:8:"Raplamaa";}i:349;O:8:"stdClass":5:{s:2:"id";i:350;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-74";s:12:"default_name";s:8:"Saaremaa";}i:350;O:8:"stdClass":5:{s:2:"id";i:351;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-78";s:12:"default_name";s:8:"Tartumaa";}i:351;O:8:"stdClass":5:{s:2:"id";i:352;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-82";s:12:"default_name";s:8:"Valgamaa";}i:352;O:8:"stdClass":5:{s:2:"id";i:353;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-84";s:12:"default_name";s:11:"Viljandimaa";}i:353;O:8:"stdClass":5:{s:2:"id";i:354;s:10:"country_id";i:74;s:12:"country_code";s:2:"EE";s:4:"code";s:5:"EE-86";s:12:"default_name";s:8:"Võrumaa";}}s:28:" * escapeWhenCastingToString";b:0;}}i:5;a:3:{s:2:"id";s:2:"FI";s:10:"admin_name";s:7:"Finland";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:20:{i:319;O:8:"stdClass":5:{s:2:"id";i:320;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:5:"Lappi";s:12:"default_name";s:5:"Lappi";}i:320;O:8:"stdClass":5:{s:2:"id";i:321;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:17:"Pohjois-Pohjanmaa";s:12:"default_name";s:17:"Pohjois-Pohjanmaa";}i:321;O:8:"stdClass":5:{s:2:"id";i:322;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:6:"Kainuu";s:12:"default_name";s:6:"Kainuu";}i:322;O:8:"stdClass":5:{s:2:"id";i:323;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:15:"Pohjois-Karjala";s:12:"default_name";s:15:"Pohjois-Karjala";}i:323;O:8:"stdClass":5:{s:2:"id";i:324;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:12:"Pohjois-Savo";s:12:"default_name";s:12:"Pohjois-Savo";}i:324;O:8:"stdClass":5:{s:2:"id";i:325;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:11:"Etelä-Savo";s:12:"default_name";s:11:"Etelä-Savo";}i:325;O:8:"stdClass":5:{s:2:"id";i:326;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:16:"Etelä-Pohjanmaa";s:12:"default_name";s:16:"Etelä-Pohjanmaa";}i:326;O:8:"stdClass":5:{s:2:"id";i:327;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:9:"Pohjanmaa";s:12:"default_name";s:9:"Pohjanmaa";}i:327;O:8:"stdClass":5:{s:2:"id";i:328;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:9:"Pirkanmaa";s:12:"default_name";s:9:"Pirkanmaa";}i:328;O:8:"stdClass":5:{s:2:"id";i:329;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:9:"Satakunta";s:12:"default_name";s:9:"Satakunta";}i:329;O:8:"stdClass":5:{s:2:"id";i:330;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:15:"Keski-Pohjanmaa";s:12:"default_name";s:15:"Keski-Pohjanmaa";}i:330;O:8:"stdClass":5:{s:2:"id";i:331;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:11:"Keski-Suomi";s:12:"default_name";s:11:"Keski-Suomi";}i:331;O:8:"stdClass":5:{s:2:"id";i:332;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:15:"Varsinais-Suomi";s:12:"default_name";s:15:"Varsinais-Suomi";}i:332;O:8:"stdClass":5:{s:2:"id";i:333;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:14:"Etelä-Karjala";s:12:"default_name";s:14:"Etelä-Karjala";}i:333;O:8:"stdClass":5:{s:2:"id";i:334;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:14:"Päijät-Häme";s:12:"default_name";s:14:"Päijät-Häme";}i:334;O:8:"stdClass":5:{s:2:"id";i:335;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:11:"Kanta-Häme";s:12:"default_name";s:11:"Kanta-Häme";}i:335;O:8:"stdClass":5:{s:2:"id";i:336;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:7:"Uusimaa";s:12:"default_name";s:7:"Uusimaa";}i:336;O:8:"stdClass":5:{s:2:"id";i:337;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:12:"Itä-Uusimaa";s:12:"default_name";s:12:"Itä-Uusimaa";}i:337;O:8:"stdClass":5:{s:2:"id";i:338;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:11:"Kymenlaakso";s:12:"default_name";s:11:"Kymenlaakso";}i:338;O:8:"stdClass":5:{s:2:"id";i:339;s:10:"country_id";i:80;s:12:"country_code";s:2:"FI";s:4:"code";s:10:"Ahvenanmaa";s:12:"default_name";s:10:"Ahvenanmaa";}}s:28:" * escapeWhenCastingToString";b:0;}}i:6;a:3:{s:2:"id";s:2:"FR";s:10:"admin_name";s:6:"France";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:96:{i:181;O:8:"stdClass":5:{s:2:"id";i:182;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"1";s:12:"default_name";s:3:"Ain";}i:182;O:8:"stdClass":5:{s:2:"id";i:183;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"2";s:12:"default_name";s:5:"Aisne";}i:183;O:8:"stdClass":5:{s:2:"id";i:184;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"3";s:12:"default_name";s:6:"Allier";}i:184;O:8:"stdClass":5:{s:2:"id";i:185;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"4";s:12:"default_name";s:23:"Alpes-de-Haute-Provence";}i:185;O:8:"stdClass":5:{s:2:"id";i:186;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"5";s:12:"default_name";s:12:"Hautes-Alpes";}i:186;O:8:"stdClass":5:{s:2:"id";i:187;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"6";s:12:"default_name";s:15:"Alpes-Maritimes";}i:187;O:8:"stdClass":5:{s:2:"id";i:188;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"7";s:12:"default_name";s:8:"Ardèche";}i:188;O:8:"stdClass":5:{s:2:"id";i:189;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"8";s:12:"default_name";s:8:"Ardennes";}i:189;O:8:"stdClass":5:{s:2:"id";i:190;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:1:"9";s:12:"default_name";s:7:"Ariège";}i:190;O:8:"stdClass":5:{s:2:"id";i:191;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"10";s:12:"default_name";s:4:"Aube";}i:191;O:8:"stdClass":5:{s:2:"id";i:192;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"11";s:12:"default_name";s:4:"Aude";}i:192;O:8:"stdClass":5:{s:2:"id";i:193;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"12";s:12:"default_name";s:7:"Aveyron";}i:193;O:8:"stdClass":5:{s:2:"id";i:194;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"13";s:12:"default_name";s:17:"Bouches-du-Rhône";}i:194;O:8:"stdClass":5:{s:2:"id";i:195;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"14";s:12:"default_name";s:8:"Calvados";}i:195;O:8:"stdClass":5:{s:2:"id";i:196;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"15";s:12:"default_name";s:6:"Cantal";}i:196;O:8:"stdClass":5:{s:2:"id";i:197;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"16";s:12:"default_name";s:8:"Charente";}i:197;O:8:"stdClass":5:{s:2:"id";i:198;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"17";s:12:"default_name";s:17:"Charente-Maritime";}i:198;O:8:"stdClass":5:{s:2:"id";i:199;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"18";s:12:"default_name";s:4:"Cher";}i:199;O:8:"stdClass":5:{s:2:"id";i:200;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"19";s:12:"default_name";s:8:"Corrèze";}i:200;O:8:"stdClass":5:{s:2:"id";i:201;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"2A";s:12:"default_name";s:12:"Corse-du-Sud";}i:201;O:8:"stdClass":5:{s:2:"id";i:202;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"2B";s:12:"default_name";s:11:"Haute-Corse";}i:202;O:8:"stdClass":5:{s:2:"id";i:203;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"21";s:12:"default_name";s:10:"Côte-d'Or";}i:203;O:8:"stdClass":5:{s:2:"id";i:204;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"22";s:12:"default_name";s:14:"Côtes-d'Armor";}i:204;O:8:"stdClass":5:{s:2:"id";i:205;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"23";s:12:"default_name";s:6:"Creuse";}i:205;O:8:"stdClass":5:{s:2:"id";i:206;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"24";s:12:"default_name";s:8:"Dordogne";}i:206;O:8:"stdClass":5:{s:2:"id";i:207;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"25";s:12:"default_name";s:5:"Doubs";}i:207;O:8:"stdClass":5:{s:2:"id";i:208;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"26";s:12:"default_name";s:6:"Drôme";}i:208;O:8:"stdClass":5:{s:2:"id";i:209;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"27";s:12:"default_name";s:4:"Eure";}i:209;O:8:"stdClass":5:{s:2:"id";i:210;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"28";s:12:"default_name";s:12:"Eure-et-Loir";}i:210;O:8:"stdClass":5:{s:2:"id";i:211;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"29";s:12:"default_name";s:10:"Finistère";}i:211;O:8:"stdClass":5:{s:2:"id";i:212;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"30";s:12:"default_name";s:4:"Gard";}i:212;O:8:"stdClass":5:{s:2:"id";i:213;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"31";s:12:"default_name";s:13:"Haute-Garonne";}i:213;O:8:"stdClass":5:{s:2:"id";i:214;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"32";s:12:"default_name";s:4:"Gers";}i:214;O:8:"stdClass":5:{s:2:"id";i:215;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"33";s:12:"default_name";s:7:"Gironde";}i:215;O:8:"stdClass":5:{s:2:"id";i:216;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"34";s:12:"default_name";s:8:"Hérault";}i:216;O:8:"stdClass":5:{s:2:"id";i:217;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"35";s:12:"default_name";s:15:"Ille-et-Vilaine";}i:217;O:8:"stdClass":5:{s:2:"id";i:218;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"36";s:12:"default_name";s:5:"Indre";}i:218;O:8:"stdClass":5:{s:2:"id";i:219;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"37";s:12:"default_name";s:14:"Indre-et-Loire";}i:219;O:8:"stdClass":5:{s:2:"id";i:220;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"38";s:12:"default_name";s:6:"Isère";}i:220;O:8:"stdClass":5:{s:2:"id";i:221;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"39";s:12:"default_name";s:4:"Jura";}i:221;O:8:"stdClass":5:{s:2:"id";i:222;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"40";s:12:"default_name";s:6:"Landes";}i:222;O:8:"stdClass":5:{s:2:"id";i:223;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"41";s:12:"default_name";s:12:"Loir-et-Cher";}i:223;O:8:"stdClass":5:{s:2:"id";i:224;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"42";s:12:"default_name";s:5:"Loire";}i:224;O:8:"stdClass":5:{s:2:"id";i:225;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"43";s:12:"default_name";s:11:"Haute-Loire";}i:225;O:8:"stdClass":5:{s:2:"id";i:226;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"44";s:12:"default_name";s:16:"Loire-Atlantique";}i:226;O:8:"stdClass":5:{s:2:"id";i:227;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"45";s:12:"default_name";s:6:"Loiret";}i:227;O:8:"stdClass":5:{s:2:"id";i:228;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"46";s:12:"default_name";s:3:"Lot";}i:228;O:8:"stdClass":5:{s:2:"id";i:229;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"47";s:12:"default_name";s:14:"Lot-et-Garonne";}i:229;O:8:"stdClass":5:{s:2:"id";i:230;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"48";s:12:"default_name";s:7:"Lozère";}i:230;O:8:"stdClass":5:{s:2:"id";i:231;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"49";s:12:"default_name";s:14:"Maine-et-Loire";}i:231;O:8:"stdClass":5:{s:2:"id";i:232;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"50";s:12:"default_name";s:6:"Manche";}i:232;O:8:"stdClass":5:{s:2:"id";i:233;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"51";s:12:"default_name";s:5:"Marne";}i:233;O:8:"stdClass":5:{s:2:"id";i:234;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"52";s:12:"default_name";s:11:"Haute-Marne";}i:234;O:8:"stdClass":5:{s:2:"id";i:235;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"53";s:12:"default_name";s:7:"Mayenne";}i:235;O:8:"stdClass":5:{s:2:"id";i:236;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"54";s:12:"default_name";s:18:"Meurthe-et-Moselle";}i:236;O:8:"stdClass":5:{s:2:"id";i:237;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"55";s:12:"default_name";s:5:"Meuse";}i:237;O:8:"stdClass":5:{s:2:"id";i:238;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"56";s:12:"default_name";s:8:"Morbihan";}i:238;O:8:"stdClass":5:{s:2:"id";i:239;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"57";s:12:"default_name";s:7:"Moselle";}i:239;O:8:"stdClass":5:{s:2:"id";i:240;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"58";s:12:"default_name";s:7:"Nièvre";}i:240;O:8:"stdClass":5:{s:2:"id";i:241;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"59";s:12:"default_name";s:4:"Nord";}i:241;O:8:"stdClass":5:{s:2:"id";i:242;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"60";s:12:"default_name";s:4:"Oise";}i:242;O:8:"stdClass":5:{s:2:"id";i:243;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"61";s:12:"default_name";s:4:"Orne";}i:243;O:8:"stdClass":5:{s:2:"id";i:244;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"62";s:12:"default_name";s:13:"Pas-de-Calais";}i:244;O:8:"stdClass":5:{s:2:"id";i:245;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"63";s:12:"default_name";s:12:"Puy-de-Dôme";}i:245;O:8:"stdClass":5:{s:2:"id";i:246;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"64";s:12:"default_name";s:22:"Pyrénées-Atlantiques";}i:246;O:8:"stdClass":5:{s:2:"id";i:247;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"65";s:12:"default_name";s:17:"Hautes-Pyrénées";}i:247;O:8:"stdClass":5:{s:2:"id";i:248;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"66";s:12:"default_name";s:21:"Pyrénées-Orientales";}i:248;O:8:"stdClass":5:{s:2:"id";i:249;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"67";s:12:"default_name";s:8:"Bas-Rhin";}i:249;O:8:"stdClass":5:{s:2:"id";i:250;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"68";s:12:"default_name";s:9:"Haut-Rhin";}i:250;O:8:"stdClass":5:{s:2:"id";i:251;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"69";s:12:"default_name";s:6:"Rhône";}i:251;O:8:"stdClass":5:{s:2:"id";i:252;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"70";s:12:"default_name";s:12:"Haute-Saône";}i:252;O:8:"stdClass":5:{s:2:"id";i:253;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"71";s:12:"default_name";s:15:"Saône-et-Loire";}i:253;O:8:"stdClass":5:{s:2:"id";i:254;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"72";s:12:"default_name";s:6:"Sarthe";}i:254;O:8:"stdClass":5:{s:2:"id";i:255;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"73";s:12:"default_name";s:6:"Savoie";}i:255;O:8:"stdClass":5:{s:2:"id";i:256;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"74";s:12:"default_name";s:12:"Haute-Savoie";}i:256;O:8:"stdClass":5:{s:2:"id";i:257;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"75";s:12:"default_name";s:5:"Paris";}i:257;O:8:"stdClass":5:{s:2:"id";i:258;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"76";s:12:"default_name";s:14:"Seine-Maritime";}i:258;O:8:"stdClass":5:{s:2:"id";i:259;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"77";s:12:"default_name";s:14:"Seine-et-Marne";}i:259;O:8:"stdClass":5:{s:2:"id";i:260;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"78";s:12:"default_name";s:8:"Yvelines";}i:260;O:8:"stdClass":5:{s:2:"id";i:261;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"79";s:12:"default_name";s:12:"Deux-Sèvres";}i:261;O:8:"stdClass":5:{s:2:"id";i:262;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"80";s:12:"default_name";s:5:"Somme";}i:262;O:8:"stdClass":5:{s:2:"id";i:263;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"81";s:12:"default_name";s:4:"Tarn";}i:263;O:8:"stdClass":5:{s:2:"id";i:264;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"82";s:12:"default_name";s:15:"Tarn-et-Garonne";}i:264;O:8:"stdClass":5:{s:2:"id";i:265;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"83";s:12:"default_name";s:3:"Var";}i:265;O:8:"stdClass":5:{s:2:"id";i:266;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"84";s:12:"default_name";s:8:"Vaucluse";}i:266;O:8:"stdClass":5:{s:2:"id";i:267;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"85";s:12:"default_name";s:7:"Vendée";}i:267;O:8:"stdClass":5:{s:2:"id";i:268;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"86";s:12:"default_name";s:6:"Vienne";}i:268;O:8:"stdClass":5:{s:2:"id";i:269;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"87";s:12:"default_name";s:12:"Haute-Vienne";}i:269;O:8:"stdClass":5:{s:2:"id";i:270;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"88";s:12:"default_name";s:6:"Vosges";}i:270;O:8:"stdClass":5:{s:2:"id";i:271;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"89";s:12:"default_name";s:5:"Yonne";}i:271;O:8:"stdClass":5:{s:2:"id";i:272;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"90";s:12:"default_name";s:21:"Territoire-de-Belfort";}i:272;O:8:"stdClass":5:{s:2:"id";i:273;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"91";s:12:"default_name";s:7:"Essonne";}i:273;O:8:"stdClass":5:{s:2:"id";i:274;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"92";s:12:"default_name";s:14:"Hauts-de-Seine";}i:274;O:8:"stdClass":5:{s:2:"id";i:275;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"93";s:12:"default_name";s:17:"Seine-Saint-Denis";}i:275;O:8:"stdClass":5:{s:2:"id";i:276;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"94";s:12:"default_name";s:12:"Val-de-Marne";}i:276;O:8:"stdClass":5:{s:2:"id";i:277;s:10:"country_id";i:81;s:12:"country_code";s:2:"FR";s:4:"code";s:2:"95";s:12:"default_name";s:10:"Val-d'Oise";}}s:28:" * escapeWhenCastingToString";b:0;}}i:7;a:3:{s:2:"id";s:2:"DE";s:10:"admin_name";s:7:"Germany";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:16:{i:78;O:8:"stdClass":5:{s:2:"id";i:79;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"NDS";s:12:"default_name";s:13:"Niedersachsen";}i:79;O:8:"stdClass":5:{s:2:"id";i:80;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"BAW";s:12:"default_name";s:18:"Baden-Württemberg";}i:80;O:8:"stdClass":5:{s:2:"id";i:81;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"BAY";s:12:"default_name";s:6:"Bayern";}i:81;O:8:"stdClass":5:{s:2:"id";i:82;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"BER";s:12:"default_name";s:6:"Berlin";}i:82;O:8:"stdClass":5:{s:2:"id";i:83;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"BRG";s:12:"default_name";s:11:"Brandenburg";}i:83;O:8:"stdClass":5:{s:2:"id";i:84;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"BRE";s:12:"default_name";s:6:"Bremen";}i:84;O:8:"stdClass":5:{s:2:"id";i:85;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"HAM";s:12:"default_name";s:7:"Hamburg";}i:85;O:8:"stdClass":5:{s:2:"id";i:86;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"HES";s:12:"default_name";s:6:"Hessen";}i:86;O:8:"stdClass":5:{s:2:"id";i:87;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"MEC";s:12:"default_name";s:22:"Mecklenburg-Vorpommern";}i:87;O:8:"stdClass":5:{s:2:"id";i:88;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"NRW";s:12:"default_name";s:19:"Nordrhein-Westfalen";}i:88;O:8:"stdClass":5:{s:2:"id";i:89;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"RHE";s:12:"default_name";s:15:"Rheinland-Pfalz";}i:89;O:8:"stdClass":5:{s:2:"id";i:90;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"SAR";s:12:"default_name";s:8:"Saarland";}i:90;O:8:"stdClass":5:{s:2:"id";i:91;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"SAS";s:12:"default_name";s:7:"Sachsen";}i:91;O:8:"stdClass":5:{s:2:"id";i:92;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"SAC";s:12:"default_name";s:14:"Sachsen-Anhalt";}i:92;O:8:"stdClass":5:{s:2:"id";i:93;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"SCN";s:12:"default_name";s:18:"Schleswig-Holstein";}i:93;O:8:"stdClass":5:{s:2:"id";i:94;s:10:"country_id";i:88;s:12:"country_code";s:2:"DE";s:4:"code";s:3:"THE";s:12:"default_name";s:10:"Thüringen";}}s:28:" * escapeWhenCastingToString";b:0;}}i:8;a:3:{s:2:"id";s:2:"IN";s:10:"admin_name";s:5:"India";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:36:{i:532;O:8:"stdClass":5:{s:2:"id";i:533;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"AN";s:12:"default_name";s:27:"Andaman and Nicobar Islands";}i:533;O:8:"stdClass":5:{s:2:"id";i:534;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"AP";s:12:"default_name";s:14:"Andhra Pradesh";}i:534;O:8:"stdClass":5:{s:2:"id";i:535;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"AR";s:12:"default_name";s:17:"Arunachal Pradesh";}i:535;O:8:"stdClass":5:{s:2:"id";i:536;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"AS";s:12:"default_name";s:5:"Assam";}i:536;O:8:"stdClass":5:{s:2:"id";i:537;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"BR";s:12:"default_name";s:5:"Bihar";}i:537;O:8:"stdClass":5:{s:2:"id";i:538;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"CH";s:12:"default_name";s:10:"Chandigarh";}i:538;O:8:"stdClass":5:{s:2:"id";i:539;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"CT";s:12:"default_name";s:12:"Chhattisgarh";}i:539;O:8:"stdClass":5:{s:2:"id";i:540;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"DN";s:12:"default_name";s:22:"Dadra and Nagar Haveli";}i:540;O:8:"stdClass":5:{s:2:"id";i:541;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"DD";s:12:"default_name";s:13:"Daman and Diu";}i:541;O:8:"stdClass":5:{s:2:"id";i:542;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"DL";s:12:"default_name";s:5:"Delhi";}i:542;O:8:"stdClass":5:{s:2:"id";i:543;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"GA";s:12:"default_name";s:3:"Goa";}i:543;O:8:"stdClass":5:{s:2:"id";i:544;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"GJ";s:12:"default_name";s:7:"Gujarat";}i:544;O:8:"stdClass":5:{s:2:"id";i:545;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"HR";s:12:"default_name";s:7:"Haryana";}i:545;O:8:"stdClass":5:{s:2:"id";i:546;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"HP";s:12:"default_name";s:16:"Himachal Pradesh";}i:546;O:8:"stdClass":5:{s:2:"id";i:547;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"JK";s:12:"default_name";s:17:"Jammu and Kashmir";}i:547;O:8:"stdClass":5:{s:2:"id";i:548;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"JH";s:12:"default_name";s:9:"Jharkhand";}i:548;O:8:"stdClass":5:{s:2:"id";i:549;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"KA";s:12:"default_name";s:9:"Karnataka";}i:549;O:8:"stdClass":5:{s:2:"id";i:550;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"KL";s:12:"default_name";s:6:"Kerala";}i:550;O:8:"stdClass":5:{s:2:"id";i:551;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"LD";s:12:"default_name";s:11:"Lakshadweep";}i:551;O:8:"stdClass":5:{s:2:"id";i:552;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"MP";s:12:"default_name";s:14:"Madhya Pradesh";}i:552;O:8:"stdClass":5:{s:2:"id";i:553;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"MH";s:12:"default_name";s:11:"Maharashtra";}i:553;O:8:"stdClass":5:{s:2:"id";i:554;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"MN";s:12:"default_name";s:7:"Manipur";}i:554;O:8:"stdClass":5:{s:2:"id";i:555;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"ML";s:12:"default_name";s:9:"Meghalaya";}i:555;O:8:"stdClass":5:{s:2:"id";i:556;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"MZ";s:12:"default_name";s:7:"Mizoram";}i:556;O:8:"stdClass":5:{s:2:"id";i:557;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"NL";s:12:"default_name";s:8:"Nagaland";}i:557;O:8:"stdClass":5:{s:2:"id";i:558;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"OR";s:12:"default_name";s:6:"Odisha";}i:558;O:8:"stdClass":5:{s:2:"id";i:559;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"PY";s:12:"default_name";s:10:"Puducherry";}i:559;O:8:"stdClass":5:{s:2:"id";i:560;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"PB";s:12:"default_name";s:6:"Punjab";}i:560;O:8:"stdClass":5:{s:2:"id";i:561;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"RJ";s:12:"default_name";s:9:"Rajasthan";}i:561;O:8:"stdClass":5:{s:2:"id";i:562;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"SK";s:12:"default_name";s:6:"Sikkim";}i:562;O:8:"stdClass":5:{s:2:"id";i:563;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"TN";s:12:"default_name";s:10:"Tamil Nadu";}i:563;O:8:"stdClass":5:{s:2:"id";i:564;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"TG";s:12:"default_name";s:9:"Telangana";}i:564;O:8:"stdClass":5:{s:2:"id";i:565;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"TR";s:12:"default_name";s:7:"Tripura";}i:565;O:8:"stdClass":5:{s:2:"id";i:566;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"UP";s:12:"default_name";s:13:"Uttar Pradesh";}i:566;O:8:"stdClass":5:{s:2:"id";i:567;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"UT";s:12:"default_name";s:11:"Uttarakhand";}i:567;O:8:"stdClass":5:{s:2:"id";i:568;s:10:"country_id";i:106;s:12:"country_code";s:2:"IN";s:4:"code";s:2:"WB";s:12:"default_name";s:11:"West Bengal";}}s:28:" * escapeWhenCastingToString";b:0;}}i:9;a:3:{s:2:"id";s:2:"LV";s:10:"admin_name";s:6:"Latvia";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:120:{i:354;O:8:"stdClass":5:{s:2:"id";i:355;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:6:"LV-DGV";s:12:"default_name";s:10:"Daugavpils";}i:355;O:8:"stdClass":5:{s:2:"id";i:356;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:6:"LV-JEL";s:12:"default_name";s:7:"Jelgava";}i:356;O:8:"stdClass":5:{s:2:"id";i:357;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:10:"Jēkabpils";s:12:"default_name";s:10:"Jēkabpils";}i:357;O:8:"stdClass":5:{s:2:"id";i:358;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:6:"LV-JUR";s:12:"default_name";s:8:"Jūrmala";}i:358;O:8:"stdClass":5:{s:2:"id";i:359;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:6:"LV-LPX";s:12:"default_name";s:8:"Liepāja";}i:359;O:8:"stdClass":5:{s:2:"id";i:360;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-LE";s:12:"default_name";s:16:"Liepājas novads";}i:360;O:8:"stdClass":5:{s:2:"id";i:361;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:6:"LV-REZ";s:12:"default_name";s:8:"Rēzekne";}i:361;O:8:"stdClass":5:{s:2:"id";i:362;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:6:"LV-RIX";s:12:"default_name";s:5:"Rīga";}i:362;O:8:"stdClass":5:{s:2:"id";i:363;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-RI";s:12:"default_name";s:13:"Rīgas novads";}i:363;O:8:"stdClass":5:{s:2:"id";i:364;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:8:"Valmiera";s:12:"default_name";s:8:"Valmiera";}i:364;O:8:"stdClass":5:{s:2:"id";i:365;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:6:"LV-VEN";s:12:"default_name";s:9:"Ventspils";}i:365;O:8:"stdClass":5:{s:2:"id";i:366;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Aglonas novads";s:12:"default_name";s:14:"Aglonas novads";}i:366;O:8:"stdClass":5:{s:2:"id";i:367;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-AI";s:12:"default_name";s:18:"Aizkraukles novads";}i:367;O:8:"stdClass":5:{s:2:"id";i:368;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Aizputes novads";s:12:"default_name";s:15:"Aizputes novads";}i:368;O:8:"stdClass":5:{s:2:"id";i:369;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Aknīstes novads";s:12:"default_name";s:16:"Aknīstes novads";}i:369;O:8:"stdClass":5:{s:2:"id";i:370;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Alojas novads";s:12:"default_name";s:13:"Alojas novads";}i:370;O:8:"stdClass":5:{s:2:"id";i:371;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Alsungas novads";s:12:"default_name";s:15:"Alsungas novads";}i:371;O:8:"stdClass":5:{s:2:"id";i:372;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-AL";s:12:"default_name";s:16:"Alūksnes novads";}i:372;O:8:"stdClass":5:{s:2:"id";i:373;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Amatas novads";s:12:"default_name";s:13:"Amatas novads";}i:373;O:8:"stdClass":5:{s:2:"id";i:374;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:11:"Apes novads";s:12:"default_name";s:11:"Apes novads";}i:374;O:8:"stdClass":5:{s:2:"id";i:375;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:12:"Auces novads";s:12:"default_name";s:12:"Auces novads";}i:375;O:8:"stdClass":5:{s:2:"id";i:376;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Babītes novads";s:12:"default_name";s:15:"Babītes novads";}i:376;O:8:"stdClass":5:{s:2:"id";i:377;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Baldones novads";s:12:"default_name";s:15:"Baldones novads";}i:377;O:8:"stdClass":5:{s:2:"id";i:378;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Baltinavas novads";s:12:"default_name";s:17:"Baltinavas novads";}i:378;O:8:"stdClass":5:{s:2:"id";i:379;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-BL";s:12:"default_name";s:12:"Balvu novads";}i:379;O:8:"stdClass":5:{s:2:"id";i:380;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-BU";s:12:"default_name";s:14:"Bauskas novads";}i:380;O:8:"stdClass":5:{s:2:"id";i:381;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Beverīnas novads";s:12:"default_name";s:17:"Beverīnas novads";}i:381;O:8:"stdClass":5:{s:2:"id";i:382;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Brocēnu novads";s:12:"default_name";s:15:"Brocēnu novads";}i:382;O:8:"stdClass":5:{s:2:"id";i:383;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Burtnieku novads";s:12:"default_name";s:16:"Burtnieku novads";}i:383;O:8:"stdClass":5:{s:2:"id";i:384;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Carnikavas novads";s:12:"default_name";s:17:"Carnikavas novads";}i:384;O:8:"stdClass":5:{s:2:"id";i:385;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Cesvaines novads";s:12:"default_name";s:16:"Cesvaines novads";}i:385;O:8:"stdClass":5:{s:2:"id";i:386;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Ciblas novads";s:12:"default_name";s:13:"Ciblas novads";}i:386;O:8:"stdClass":5:{s:2:"id";i:387;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-CE";s:12:"default_name";s:12:"Cēsu novads";}i:387;O:8:"stdClass":5:{s:2:"id";i:388;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Dagdas novads";s:12:"default_name";s:13:"Dagdas novads";}i:388;O:8:"stdClass":5:{s:2:"id";i:389;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-DA";s:12:"default_name";s:17:"Daugavpils novads";}i:389;O:8:"stdClass":5:{s:2:"id";i:390;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-DO";s:12:"default_name";s:14:"Dobeles novads";}i:390;O:8:"stdClass":5:{s:2:"id";i:391;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Dundagas novads";s:12:"default_name";s:15:"Dundagas novads";}i:391;O:8:"stdClass":5:{s:2:"id";i:392;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Durbes novads";s:12:"default_name";s:13:"Durbes novads";}i:392;O:8:"stdClass":5:{s:2:"id";i:393;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Engures novads";s:12:"default_name";s:14:"Engures novads";}i:393;O:8:"stdClass":5:{s:2:"id";i:394;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Garkalnes novads";s:12:"default_name";s:16:"Garkalnes novads";}i:394;O:8:"stdClass":5:{s:2:"id";i:395;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Grobiņas novads";s:12:"default_name";s:16:"Grobiņas novads";}i:395;O:8:"stdClass":5:{s:2:"id";i:396;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-GU";s:12:"default_name";s:15:"Gulbenes novads";}i:396;O:8:"stdClass":5:{s:2:"id";i:397;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Iecavas novads";s:12:"default_name";s:14:"Iecavas novads";}i:397;O:8:"stdClass":5:{s:2:"id";i:398;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Ikšķiles novads";s:12:"default_name";s:17:"Ikšķiles novads";}i:398;O:8:"stdClass":5:{s:2:"id";i:399;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Ilūkstes novads";s:12:"default_name";s:16:"Ilūkstes novads";}i:399;O:8:"stdClass":5:{s:2:"id";i:400;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Inčukalna novads";s:12:"default_name";s:17:"Inčukalna novads";}i:400;O:8:"stdClass":5:{s:2:"id";i:401;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:19:"Jaunjelgavas novads";s:12:"default_name";s:19:"Jaunjelgavas novads";}i:401;O:8:"stdClass":5:{s:2:"id";i:402;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:20:"Jaunpiebalgas novads";s:12:"default_name";s:20:"Jaunpiebalgas novads";}i:402;O:8:"stdClass":5:{s:2:"id";i:403;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Jaunpils novads";s:12:"default_name";s:15:"Jaunpils novads";}i:403;O:8:"stdClass":5:{s:2:"id";i:404;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-JL";s:12:"default_name";s:15:"Jelgavas novads";}i:404;O:8:"stdClass":5:{s:2:"id";i:405;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-JK";s:12:"default_name";s:17:"Jēkabpils novads";}i:405;O:8:"stdClass":5:{s:2:"id";i:406;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Kandavas novads";s:12:"default_name";s:15:"Kandavas novads";}i:406;O:8:"stdClass":5:{s:2:"id";i:407;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Kokneses novads";s:12:"default_name";s:15:"Kokneses novads";}i:407;O:8:"stdClass":5:{s:2:"id";i:408;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Krimuldas novads";s:12:"default_name";s:16:"Krimuldas novads";}i:408;O:8:"stdClass":5:{s:2:"id";i:409;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Krustpils novads";s:12:"default_name";s:16:"Krustpils novads";}i:409;O:8:"stdClass":5:{s:2:"id";i:410;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-KR";s:12:"default_name";s:17:"Krāslavas novads";}i:410;O:8:"stdClass":5:{s:2:"id";i:411;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-KU";s:12:"default_name";s:16:"Kuldīgas novads";}i:411;O:8:"stdClass":5:{s:2:"id";i:412;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Kārsavas novads";s:12:"default_name";s:16:"Kārsavas novads";}i:412;O:8:"stdClass":5:{s:2:"id";i:413;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:18:"Lielvārdes novads";s:12:"default_name";s:18:"Lielvārdes novads";}i:413;O:8:"stdClass":5:{s:2:"id";i:414;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-LM";s:12:"default_name";s:15:"Limbažu novads";}i:414;O:8:"stdClass":5:{s:2:"id";i:415;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Lubānas novads";s:12:"default_name";s:15:"Lubānas novads";}i:415;O:8:"stdClass":5:{s:2:"id";i:416;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-LU";s:12:"default_name";s:13:"Ludzas novads";}i:416;O:8:"stdClass":5:{s:2:"id";i:417;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Līgatnes novads";s:12:"default_name";s:16:"Līgatnes novads";}i:417;O:8:"stdClass":5:{s:2:"id";i:418;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Līvānu novads";s:12:"default_name";s:15:"Līvānu novads";}i:418;O:8:"stdClass":5:{s:2:"id";i:419;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-MA";s:12:"default_name";s:14:"Madonas novads";}i:419;O:8:"stdClass":5:{s:2:"id";i:420;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Mazsalacas novads";s:12:"default_name";s:17:"Mazsalacas novads";}i:420;O:8:"stdClass":5:{s:2:"id";i:421;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Mālpils novads";s:12:"default_name";s:15:"Mālpils novads";}i:421;O:8:"stdClass":5:{s:2:"id";i:422;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Mārupes novads";s:12:"default_name";s:15:"Mārupes novads";}i:422;O:8:"stdClass":5:{s:2:"id";i:423;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Naukšēnu novads";s:12:"default_name";s:17:"Naukšēnu novads";}i:423;O:8:"stdClass":5:{s:2:"id";i:424;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Neretas novads";s:12:"default_name";s:14:"Neretas novads";}i:424;O:8:"stdClass":5:{s:2:"id";i:425;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Nīcas novads";s:12:"default_name";s:13:"Nīcas novads";}i:425;O:8:"stdClass":5:{s:2:"id";i:426;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-OG";s:12:"default_name";s:12:"Ogres novads";}i:426;O:8:"stdClass":5:{s:2:"id";i:427;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Olaines novads";s:12:"default_name";s:14:"Olaines novads";}i:427;O:8:"stdClass":5:{s:2:"id";i:428;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Ozolnieku novads";s:12:"default_name";s:16:"Ozolnieku novads";}i:428;O:8:"stdClass":5:{s:2:"id";i:429;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-PR";s:12:"default_name";s:14:"Preiļu novads";}i:429;O:8:"stdClass":5:{s:2:"id";i:430;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Priekules novads";s:12:"default_name";s:16:"Priekules novads";}i:430;O:8:"stdClass":5:{s:2:"id";i:431;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Priekuļu novads";s:12:"default_name";s:16:"Priekuļu novads";}i:431;O:8:"stdClass":5:{s:2:"id";i:432;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Pārgaujas novads";s:12:"default_name";s:17:"Pārgaujas novads";}i:432;O:8:"stdClass":5:{s:2:"id";i:433;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:18:"Pāvilostas novads";s:12:"default_name";s:18:"Pāvilostas novads";}i:433;O:8:"stdClass":5:{s:2:"id";i:434;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Pļaviņu novads";s:12:"default_name";s:16:"Pļaviņu novads";}i:434;O:8:"stdClass":5:{s:2:"id";i:435;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Raunas novads";s:12:"default_name";s:13:"Raunas novads";}i:435;O:8:"stdClass":5:{s:2:"id";i:436;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Riebiņu novads";s:12:"default_name";s:15:"Riebiņu novads";}i:436;O:8:"stdClass":5:{s:2:"id";i:437;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:12:"Rojas novads";s:12:"default_name";s:12:"Rojas novads";}i:437;O:8:"stdClass":5:{s:2:"id";i:438;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Ropažu novads";s:12:"default_name";s:14:"Ropažu novads";}i:438;O:8:"stdClass":5:{s:2:"id";i:439;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Rucavas novads";s:12:"default_name";s:14:"Rucavas novads";}i:439;O:8:"stdClass":5:{s:2:"id";i:440;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Rugāju novads";s:12:"default_name";s:14:"Rugāju novads";}i:440;O:8:"stdClass":5:{s:2:"id";i:441;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Rundāles novads";s:12:"default_name";s:16:"Rundāles novads";}i:441;O:8:"stdClass":5:{s:2:"id";i:442;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-RE";s:12:"default_name";s:16:"Rēzeknes novads";}i:442;O:8:"stdClass":5:{s:2:"id";i:443;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Rūjienas novads";s:12:"default_name";s:16:"Rūjienas novads";}i:443;O:8:"stdClass":5:{s:2:"id";i:444;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:19:"Salacgrīvas novads";s:12:"default_name";s:19:"Salacgrīvas novads";}i:444;O:8:"stdClass":5:{s:2:"id";i:445;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:12:"Salas novads";s:12:"default_name";s:12:"Salas novads";}i:445;O:8:"stdClass":5:{s:2:"id";i:446;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Salaspils novads";s:12:"default_name";s:16:"Salaspils novads";}i:446;O:8:"stdClass":5:{s:2:"id";i:447;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-SA";s:12:"default_name";s:13:"Saldus novads";}i:447;O:8:"stdClass":5:{s:2:"id";i:448;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Saulkrastu novads";s:12:"default_name";s:17:"Saulkrastu novads";}i:448;O:8:"stdClass":5:{s:2:"id";i:449;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Siguldas novads";s:12:"default_name";s:15:"Siguldas novads";}i:449;O:8:"stdClass":5:{s:2:"id";i:450;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Skrundas novads";s:12:"default_name";s:15:"Skrundas novads";}i:450;O:8:"stdClass":5:{s:2:"id";i:451;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Skrīveru novads";s:12:"default_name";s:16:"Skrīveru novads";}i:451;O:8:"stdClass":5:{s:2:"id";i:452;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Smiltenes novads";s:12:"default_name";s:16:"Smiltenes novads";}i:452;O:8:"stdClass":5:{s:2:"id";i:453;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Stopiņu novads";s:12:"default_name";s:15:"Stopiņu novads";}i:453;O:8:"stdClass":5:{s:2:"id";i:454;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Strenču novads";s:12:"default_name";s:15:"Strenču novads";}i:454;O:8:"stdClass":5:{s:2:"id";i:455;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:13:"Sējas novads";s:12:"default_name";s:13:"Sējas novads";}i:455;O:8:"stdClass":5:{s:2:"id";i:456;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-TA";s:12:"default_name";s:12:"Talsu novads";}i:456;O:8:"stdClass":5:{s:2:"id";i:457;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-TU";s:12:"default_name";s:13:"Tukuma novads";}i:457;O:8:"stdClass":5:{s:2:"id";i:458;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Tērvetes novads";s:12:"default_name";s:16:"Tērvetes novads";}i:458;O:8:"stdClass":5:{s:2:"id";i:459;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Vaiņodes novads";s:12:"default_name";s:16:"Vaiņodes novads";}i:459;O:8:"stdClass":5:{s:2:"id";i:460;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-VK";s:12:"default_name";s:13:"Valkas novads";}i:460;O:8:"stdClass":5:{s:2:"id";i:461;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-VM";s:12:"default_name";s:16:"Valmieras novads";}i:461;O:8:"stdClass":5:{s:2:"id";i:462;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:18:"Varakļānu novads";s:12:"default_name";s:18:"Varakļānu novads";}i:462;O:8:"stdClass":5:{s:2:"id";i:463;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:19:"Vecpiebalgas novads";s:12:"default_name";s:19:"Vecpiebalgas novads";}i:463;O:8:"stdClass":5:{s:2:"id";i:464;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:17:"Vecumnieku novads";s:12:"default_name";s:17:"Vecumnieku novads";}i:464;O:8:"stdClass":5:{s:2:"id";i:465;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:5:"LV-VE";s:12:"default_name";s:16:"Ventspils novads";}i:465;O:8:"stdClass":5:{s:2:"id";i:466;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Viesītes novads";s:12:"default_name";s:16:"Viesītes novads";}i:466;O:8:"stdClass":5:{s:2:"id";i:467;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Viļakas novads";s:12:"default_name";s:15:"Viļakas novads";}i:467;O:8:"stdClass":5:{s:2:"id";i:468;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Viļānu novads";s:12:"default_name";s:15:"Viļānu novads";}i:468;O:8:"stdClass":5:{s:2:"id";i:469;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:16:"Vārkavas novads";s:12:"default_name";s:16:"Vārkavas novads";}i:469;O:8:"stdClass":5:{s:2:"id";i:470;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Zilupes novads";s:12:"default_name";s:14:"Zilupes novads";}i:470;O:8:"stdClass":5:{s:2:"id";i:471;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Ādažu novads";s:12:"default_name";s:14:"Ādažu novads";}i:471;O:8:"stdClass":5:{s:2:"id";i:472;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Ērgļu novads";s:12:"default_name";s:14:"Ērgļu novads";}i:472;O:8:"stdClass":5:{s:2:"id";i:473;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:14:"Ķeguma novads";s:12:"default_name";s:14:"Ķeguma novads";}i:473;O:8:"stdClass":5:{s:2:"id";i:474;s:10:"country_id";i:125;s:12:"country_code";s:2:"LV";s:4:"code";s:15:"Ķekavas novads";s:12:"default_name";s:15:"Ķekavas novads";}}s:28:" * escapeWhenCastingToString";b:0;}}i:10;a:3:{s:2:"id";s:2:"LT";s:10:"admin_name";s:9:"Lithuania";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:10:{i:474;O:8:"stdClass":5:{s:2:"id";i:475;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-AL";s:12:"default_name";s:17:"Alytaus Apskritis";}i:475;O:8:"stdClass":5:{s:2:"id";i:476;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-KU";s:12:"default_name";s:15:"Kauno Apskritis";}i:476;O:8:"stdClass":5:{s:2:"id";i:477;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-KL";s:12:"default_name";s:20:"Klaipėdos Apskritis";}i:477;O:8:"stdClass":5:{s:2:"id";i:478;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-MR";s:12:"default_name";s:23:"Marijampolės Apskritis";}i:478;O:8:"stdClass":5:{s:2:"id";i:479;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-PN";s:12:"default_name";s:21:"Panevėžio Apskritis";}i:479;O:8:"stdClass":5:{s:2:"id";i:480;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-SA";s:12:"default_name";s:19:"Šiaulių Apskritis";}i:480;O:8:"stdClass":5:{s:2:"id";i:481;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-TA";s:12:"default_name";s:19:"Tauragės Apskritis";}i:481;O:8:"stdClass":5:{s:2:"id";i:482;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-TE";s:12:"default_name";s:18:"Telšių Apskritis";}i:482;O:8:"stdClass":5:{s:2:"id";i:483;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-UT";s:12:"default_name";s:16:"Utenos Apskritis";}i:483;O:8:"stdClass":5:{s:2:"id";i:484;s:10:"country_id";i:131;s:12:"country_code";s:2:"LT";s:4:"code";s:5:"LT-VL";s:12:"default_name";s:18:"Vilniaus Apskritis";}}s:28:" * escapeWhenCastingToString";b:0;}}i:11;a:3:{s:2:"id";s:2:"PY";s:10:"admin_name";s:8:"Paraguay";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:18:{i:568;O:8:"stdClass":5:{s:2:"id";i:569;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-16";s:12:"default_name";s:13:"Alto Paraguay";}i:569;O:8:"stdClass":5:{s:2:"id";i:570;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-10";s:12:"default_name";s:12:"Alto Paraná";}i:570;O:8:"stdClass":5:{s:2:"id";i:571;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-13";s:12:"default_name";s:7:"Amambay";}i:571;O:8:"stdClass":5:{s:2:"id";i:572;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:6:"PY-ASU";s:12:"default_name";s:9:"Asunción";}i:572;O:8:"stdClass":5:{s:2:"id";i:573;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-19";s:12:"default_name";s:9:"Boquerón";}i:573;O:8:"stdClass":5:{s:2:"id";i:574;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-5";s:12:"default_name";s:9:"Caaguazú";}i:574;O:8:"stdClass":5:{s:2:"id";i:575;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-6";s:12:"default_name";s:8:"Caazapá";}i:575;O:8:"stdClass":5:{s:2:"id";i:576;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-14";s:12:"default_name";s:10:"Canindeyú";}i:576;O:8:"stdClass":5:{s:2:"id";i:577;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-11";s:12:"default_name";s:7:"Central";}i:577;O:8:"stdClass":5:{s:2:"id";i:578;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-1";s:12:"default_name";s:11:"Concepción";}i:578;O:8:"stdClass":5:{s:2:"id";i:579;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-3";s:12:"default_name";s:10:"Cordillera";}i:579;O:8:"stdClass":5:{s:2:"id";i:580;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-4";s:12:"default_name";s:7:"Guairá";}i:580;O:8:"stdClass":5:{s:2:"id";i:581;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-7";s:12:"default_name";s:7:"Itapúa";}i:581;O:8:"stdClass":5:{s:2:"id";i:582;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-8";s:12:"default_name";s:8:"Misiones";}i:582;O:8:"stdClass":5:{s:2:"id";i:583;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-9";s:12:"default_name";s:10:"Paraguarí";}i:583;O:8:"stdClass":5:{s:2:"id";i:584;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-15";s:12:"default_name";s:16:"Presidente Hayes";}i:584;O:8:"stdClass":5:{s:2:"id";i:585;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:4:"PY-2";s:12:"default_name";s:9:"San Pedro";}i:585;O:8:"stdClass":5:{s:2:"id";i:586;s:10:"country_id";i:176;s:12:"country_code";s:2:"PY";s:4:"code";s:5:"PY-12";s:12:"default_name";s:10:"Ñeembucú";}}s:28:" * escapeWhenCastingToString";b:0;}}i:12;a:3:{s:2:"id";s:2:"RO";s:10:"admin_name";s:7:"Romania";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:42:{i:277;O:8:"stdClass":5:{s:2:"id";i:278;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"AB";s:12:"default_name";s:4:"Alba";}i:278;O:8:"stdClass":5:{s:2:"id";i:279;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"AR";s:12:"default_name";s:4:"Arad";}i:279;O:8:"stdClass":5:{s:2:"id";i:280;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"AG";s:12:"default_name";s:6:"Argeş";}i:280;O:8:"stdClass":5:{s:2:"id";i:281;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"BC";s:12:"default_name";s:6:"Bacău";}i:281;O:8:"stdClass":5:{s:2:"id";i:282;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"BH";s:12:"default_name";s:5:"Bihor";}i:282;O:8:"stdClass":5:{s:2:"id";i:283;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"BN";s:12:"default_name";s:18:"Bistriţa-Năsăud";}i:283;O:8:"stdClass":5:{s:2:"id";i:284;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"BT";s:12:"default_name";s:9:"Botoşani";}i:284;O:8:"stdClass":5:{s:2:"id";i:285;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"BV";s:12:"default_name";s:7:"Braşov";}i:285;O:8:"stdClass":5:{s:2:"id";i:286;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"BR";s:12:"default_name";s:7:"Brăila";}i:286;O:8:"stdClass":5:{s:2:"id";i:287;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:1:"B";s:12:"default_name";s:10:"Bucureşti";}i:287;O:8:"stdClass":5:{s:2:"id";i:288;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"BZ";s:12:"default_name";s:6:"Buzău";}i:288;O:8:"stdClass":5:{s:2:"id";i:289;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"CS";s:12:"default_name";s:14:"Caraş-Severin";}i:289;O:8:"stdClass":5:{s:2:"id";i:290;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"CL";s:12:"default_name";s:11:"Călăraşi";}i:290;O:8:"stdClass":5:{s:2:"id";i:291;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"CJ";s:12:"default_name";s:4:"Cluj";}i:291;O:8:"stdClass":5:{s:2:"id";i:292;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"CT";s:12:"default_name";s:10:"Constanţa";}i:292;O:8:"stdClass":5:{s:2:"id";i:293;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"CV";s:12:"default_name";s:7:"Covasna";}i:293;O:8:"stdClass":5:{s:2:"id";i:294;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"DB";s:12:"default_name";s:11:"Dâmboviţa";}i:294;O:8:"stdClass":5:{s:2:"id";i:295;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"DJ";s:12:"default_name";s:4:"Dolj";}i:295;O:8:"stdClass":5:{s:2:"id";i:296;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"GL";s:12:"default_name";s:7:"Galaţi";}i:296;O:8:"stdClass":5:{s:2:"id";i:297;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"GR";s:12:"default_name";s:7:"Giurgiu";}i:297;O:8:"stdClass":5:{s:2:"id";i:298;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"GJ";s:12:"default_name";s:4:"Gorj";}i:298;O:8:"stdClass":5:{s:2:"id";i:299;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"HR";s:12:"default_name";s:8:"Harghita";}i:299;O:8:"stdClass":5:{s:2:"id";i:300;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"HD";s:12:"default_name";s:9:"Hunedoara";}i:300;O:8:"stdClass":5:{s:2:"id";i:301;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"IL";s:12:"default_name";s:9:"Ialomiţa";}i:301;O:8:"stdClass":5:{s:2:"id";i:302;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"IS";s:12:"default_name";s:5:"Iaşi";}i:302;O:8:"stdClass":5:{s:2:"id";i:303;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"IF";s:12:"default_name";s:5:"Ilfov";}i:303;O:8:"stdClass":5:{s:2:"id";i:304;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"MM";s:12:"default_name";s:10:"Maramureş";}i:304;O:8:"stdClass":5:{s:2:"id";i:305;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"MH";s:12:"default_name";s:10:"Mehedinţi";}i:305;O:8:"stdClass":5:{s:2:"id";i:306;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"MS";s:12:"default_name";s:6:"Mureş";}i:306;O:8:"stdClass":5:{s:2:"id";i:307;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"NT";s:12:"default_name";s:6:"Neamţ";}i:307;O:8:"stdClass":5:{s:2:"id";i:308;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"OT";s:12:"default_name";s:3:"Olt";}i:308;O:8:"stdClass":5:{s:2:"id";i:309;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"PH";s:12:"default_name";s:7:"Prahova";}i:309;O:8:"stdClass":5:{s:2:"id";i:310;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"SM";s:12:"default_name";s:9:"Satu-Mare";}i:310;O:8:"stdClass":5:{s:2:"id";i:311;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"SJ";s:12:"default_name";s:6:"Sălaj";}i:311;O:8:"stdClass":5:{s:2:"id";i:312;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"SB";s:12:"default_name";s:5:"Sibiu";}i:312;O:8:"stdClass":5:{s:2:"id";i:313;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"SV";s:12:"default_name";s:7:"Suceava";}i:313;O:8:"stdClass":5:{s:2:"id";i:314;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"TR";s:12:"default_name";s:9:"Teleorman";}i:314;O:8:"stdClass":5:{s:2:"id";i:315;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"TM";s:12:"default_name";s:6:"Timiş";}i:315;O:8:"stdClass":5:{s:2:"id";i:316;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"TL";s:12:"default_name";s:6:"Tulcea";}i:316;O:8:"stdClass":5:{s:2:"id";i:317;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"VS";s:12:"default_name";s:6:"Vaslui";}i:317;O:8:"stdClass":5:{s:2:"id";i:318;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"VL";s:12:"default_name";s:7:"Vâlcea";}i:318;O:8:"stdClass":5:{s:2:"id";i:319;s:10:"country_id";i:185;s:12:"country_code";s:2:"RO";s:4:"code";s:2:"VN";s:12:"default_name";s:7:"Vrancea";}}s:28:" * escapeWhenCastingToString";b:0;}}i:13;a:3:{s:2:"id";s:2:"ES";s:10:"admin_name";s:5:"Spain";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:52:{i:129;O:8:"stdClass":5:{s:2:"id";i:130;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:9:"A Coruсa";s:12:"default_name";s:9:"A Coruña";}i:130;O:8:"stdClass":5:{s:2:"id";i:131;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:5:"Alava";s:12:"default_name";s:5:"Alava";}i:131;O:8:"stdClass":5:{s:2:"id";i:132;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"Albacete";s:12:"default_name";s:8:"Albacete";}i:132;O:8:"stdClass":5:{s:2:"id";i:133;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"Alicante";s:12:"default_name";s:8:"Alicante";}i:133;O:8:"stdClass":5:{s:2:"id";i:134;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Almeria";s:12:"default_name";s:7:"Almeria";}i:134;O:8:"stdClass":5:{s:2:"id";i:135;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"Asturias";s:12:"default_name";s:8:"Asturias";}i:135;O:8:"stdClass":5:{s:2:"id";i:136;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:5:"Avila";s:12:"default_name";s:5:"Avila";}i:136;O:8:"stdClass":5:{s:2:"id";i:137;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Badajoz";s:12:"default_name";s:7:"Badajoz";}i:137;O:8:"stdClass":5:{s:2:"id";i:138;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"Baleares";s:12:"default_name";s:8:"Baleares";}i:138;O:8:"stdClass":5:{s:2:"id";i:139;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:9:"Barcelona";s:12:"default_name";s:9:"Barcelona";}i:139;O:8:"stdClass":5:{s:2:"id";i:140;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Burgos";s:12:"default_name";s:6:"Burgos";}i:140;O:8:"stdClass":5:{s:2:"id";i:141;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Caceres";s:12:"default_name";s:7:"Caceres";}i:141;O:8:"stdClass":5:{s:2:"id";i:142;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:5:"Cadiz";s:12:"default_name";s:5:"Cadiz";}i:142;O:8:"stdClass":5:{s:2:"id";i:143;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:9:"Cantabria";s:12:"default_name";s:9:"Cantabria";}i:143;O:8:"stdClass":5:{s:2:"id";i:144;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:9:"Castellon";s:12:"default_name";s:9:"Castellon";}i:144;O:8:"stdClass":5:{s:2:"id";i:145;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:5:"Ceuta";s:12:"default_name";s:5:"Ceuta";}i:145;O:8:"stdClass":5:{s:2:"id";i:146;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:11:"Ciudad Real";s:12:"default_name";s:11:"Ciudad Real";}i:146;O:8:"stdClass":5:{s:2:"id";i:147;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Cordoba";s:12:"default_name";s:7:"Cordoba";}i:147;O:8:"stdClass":5:{s:2:"id";i:148;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Cuenca";s:12:"default_name";s:6:"Cuenca";}i:148;O:8:"stdClass":5:{s:2:"id";i:149;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Girona";s:12:"default_name";s:6:"Girona";}i:149;O:8:"stdClass":5:{s:2:"id";i:150;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Granada";s:12:"default_name";s:7:"Granada";}i:150;O:8:"stdClass":5:{s:2:"id";i:151;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:11:"Guadalajara";s:12:"default_name";s:11:"Guadalajara";}i:151;O:8:"stdClass":5:{s:2:"id";i:152;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:9:"Guipuzcoa";s:12:"default_name";s:9:"Guipuzcoa";}i:152;O:8:"stdClass":5:{s:2:"id";i:153;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Huelva";s:12:"default_name";s:6:"Huelva";}i:153;O:8:"stdClass":5:{s:2:"id";i:154;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Huesca";s:12:"default_name";s:6:"Huesca";}i:154;O:8:"stdClass":5:{s:2:"id";i:155;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:4:"Jaen";s:12:"default_name";s:4:"Jaen";}i:155;O:8:"stdClass":5:{s:2:"id";i:156;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"La Rioja";s:12:"default_name";s:8:"La Rioja";}i:156;O:8:"stdClass":5:{s:2:"id";i:157;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:10:"Las Palmas";s:12:"default_name";s:10:"Las Palmas";}i:157;O:8:"stdClass":5:{s:2:"id";i:158;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:4:"Leon";s:12:"default_name";s:4:"Leon";}i:158;O:8:"stdClass":5:{s:2:"id";i:159;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Lleida";s:12:"default_name";s:6:"Lleida";}i:159;O:8:"stdClass":5:{s:2:"id";i:160;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:4:"Lugo";s:12:"default_name";s:4:"Lugo";}i:160;O:8:"stdClass":5:{s:2:"id";i:161;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Madrid";s:12:"default_name";s:6:"Madrid";}i:161;O:8:"stdClass":5:{s:2:"id";i:162;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Malaga";s:12:"default_name";s:6:"Malaga";}i:162;O:8:"stdClass":5:{s:2:"id";i:163;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Melilla";s:12:"default_name";s:7:"Melilla";}i:163;O:8:"stdClass":5:{s:2:"id";i:164;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Murcia";s:12:"default_name";s:6:"Murcia";}i:164;O:8:"stdClass":5:{s:2:"id";i:165;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Navarra";s:12:"default_name";s:7:"Navarra";}i:165;O:8:"stdClass":5:{s:2:"id";i:166;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Ourense";s:12:"default_name";s:7:"Ourense";}i:166;O:8:"stdClass":5:{s:2:"id";i:167;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"Palencia";s:12:"default_name";s:8:"Palencia";}i:167;O:8:"stdClass":5:{s:2:"id";i:168;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:10:"Pontevedra";s:12:"default_name";s:10:"Pontevedra";}i:168;O:8:"stdClass":5:{s:2:"id";i:169;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:9:"Salamanca";s:12:"default_name";s:9:"Salamanca";}i:169;O:8:"stdClass":5:{s:2:"id";i:170;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:22:"Santa Cruz de Tenerife";s:12:"default_name";s:22:"Santa Cruz de Tenerife";}i:170;O:8:"stdClass":5:{s:2:"id";i:171;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Segovia";s:12:"default_name";s:7:"Segovia";}i:171;O:8:"stdClass":5:{s:2:"id";i:172;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Sevilla";s:12:"default_name";s:7:"Sevilla";}i:172;O:8:"stdClass":5:{s:2:"id";i:173;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:5:"Soria";s:12:"default_name";s:5:"Soria";}i:173;O:8:"stdClass":5:{s:2:"id";i:174;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:9:"Tarragona";s:12:"default_name";s:9:"Tarragona";}i:174;O:8:"stdClass":5:{s:2:"id";i:175;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Teruel";s:12:"default_name";s:6:"Teruel";}i:175;O:8:"stdClass":5:{s:2:"id";i:176;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Toledo";s:12:"default_name";s:6:"Toledo";}i:176;O:8:"stdClass":5:{s:2:"id";i:177;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"Valencia";s:12:"default_name";s:8:"Valencia";}i:177;O:8:"stdClass":5:{s:2:"id";i:178;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:10:"Valladolid";s:12:"default_name";s:10:"Valladolid";}i:178;O:8:"stdClass":5:{s:2:"id";i:179;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:7:"Vizcaya";s:12:"default_name";s:7:"Vizcaya";}i:179;O:8:"stdClass":5:{s:2:"id";i:180;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:6:"Zamora";s:12:"default_name";s:6:"Zamora";}i:180;O:8:"stdClass":5:{s:2:"id";i:181;s:10:"country_id";i:206;s:12:"country_code";s:2:"ES";s:4:"code";s:8:"Zaragoza";s:12:"default_name";s:8:"Zaragoza";}}s:28:" * escapeWhenCastingToString";b:0;}}i:14;a:3:{s:2:"id";s:2:"CH";s:10:"admin_name";s:11:"Switzerland";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:26:{i:103;O:8:"stdClass":5:{s:2:"id";i:104;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"AG";s:12:"default_name";s:6:"Aargau";}i:104;O:8:"stdClass":5:{s:2:"id";i:105;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"AI";s:12:"default_name";s:21:"Appenzell Innerrhoden";}i:105;O:8:"stdClass":5:{s:2:"id";i:106;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"AR";s:12:"default_name";s:22:"Appenzell Ausserrhoden";}i:106;O:8:"stdClass":5:{s:2:"id";i:107;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"BE";s:12:"default_name";s:4:"Bern";}i:107;O:8:"stdClass":5:{s:2:"id";i:108;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"BL";s:12:"default_name";s:16:"Basel-Landschaft";}i:108;O:8:"stdClass":5:{s:2:"id";i:109;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"BS";s:12:"default_name";s:11:"Basel-Stadt";}i:109;O:8:"stdClass":5:{s:2:"id";i:110;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"FR";s:12:"default_name";s:8:"Freiburg";}i:110;O:8:"stdClass":5:{s:2:"id";i:111;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"GE";s:12:"default_name";s:4:"Genf";}i:111;O:8:"stdClass":5:{s:2:"id";i:112;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"GL";s:12:"default_name";s:6:"Glarus";}i:112;O:8:"stdClass":5:{s:2:"id";i:113;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"GR";s:12:"default_name";s:11:"Graubünden";}i:113;O:8:"stdClass":5:{s:2:"id";i:114;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"JU";s:12:"default_name";s:4:"Jura";}i:114;O:8:"stdClass":5:{s:2:"id";i:115;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"LU";s:12:"default_name";s:6:"Luzern";}i:115;O:8:"stdClass":5:{s:2:"id";i:116;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"NE";s:12:"default_name";s:9:"Neuenburg";}i:116;O:8:"stdClass":5:{s:2:"id";i:117;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"NW";s:12:"default_name";s:9:"Nidwalden";}i:117;O:8:"stdClass":5:{s:2:"id";i:118;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"OW";s:12:"default_name";s:8:"Obwalden";}i:118;O:8:"stdClass":5:{s:2:"id";i:119;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"SG";s:12:"default_name";s:10:"St. Gallen";}i:119;O:8:"stdClass":5:{s:2:"id";i:120;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"SH";s:12:"default_name";s:12:"Schaffhausen";}i:120;O:8:"stdClass":5:{s:2:"id";i:121;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"SO";s:12:"default_name";s:9:"Solothurn";}i:121;O:8:"stdClass":5:{s:2:"id";i:122;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"SZ";s:12:"default_name";s:6:"Schwyz";}i:122;O:8:"stdClass":5:{s:2:"id";i:123;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"TG";s:12:"default_name";s:7:"Thurgau";}i:123;O:8:"stdClass":5:{s:2:"id";i:124;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"TI";s:12:"default_name";s:6:"Tessin";}i:124;O:8:"stdClass":5:{s:2:"id";i:125;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"UR";s:12:"default_name";s:3:"Uri";}i:125;O:8:"stdClass":5:{s:2:"id";i:126;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"VD";s:12:"default_name";s:5:"Waadt";}i:126;O:8:"stdClass":5:{s:2:"id";i:127;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"VS";s:12:"default_name";s:6:"Wallis";}i:127;O:8:"stdClass":5:{s:2:"id";i:128;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"ZG";s:12:"default_name";s:3:"Zug";}i:128;O:8:"stdClass":5:{s:2:"id";i:129;s:10:"country_id";i:220;s:12:"country_code";s:2:"CH";s:4:"code";s:2:"ZH";s:12:"default_name";s:7:"Zürich";}}s:28:" * escapeWhenCastingToString";b:0;}}i:15;a:3:{s:2:"id";s:2:"US";s:10:"admin_name";s:13:"United States";s:6:"states";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:65:{i:0;O:8:"stdClass":5:{s:2:"id";i:1;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AL";s:12:"default_name";s:7:"Alabama";}i:1;O:8:"stdClass":5:{s:2:"id";i:2;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AK";s:12:"default_name";s:6:"Alaska";}i:2;O:8:"stdClass":5:{s:2:"id";i:3;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AS";s:12:"default_name";s:14:"American Samoa";}i:3;O:8:"stdClass":5:{s:2:"id";i:4;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AZ";s:12:"default_name";s:7:"Arizona";}i:4;O:8:"stdClass":5:{s:2:"id";i:5;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AR";s:12:"default_name";s:8:"Arkansas";}i:5;O:8:"stdClass":5:{s:2:"id";i:6;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AE";s:12:"default_name";s:19:"Armed Forces Africa";}i:6;O:8:"stdClass":5:{s:2:"id";i:7;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AA";s:12:"default_name";s:21:"Armed Forces Americas";}i:7;O:8:"stdClass":5:{s:2:"id";i:8;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AE";s:12:"default_name";s:19:"Armed Forces Canada";}i:8;O:8:"stdClass":5:{s:2:"id";i:9;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AE";s:12:"default_name";s:19:"Armed Forces Europe";}i:9;O:8:"stdClass":5:{s:2:"id";i:10;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AE";s:12:"default_name";s:24:"Armed Forces Middle East";}i:10;O:8:"stdClass":5:{s:2:"id";i:11;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"AP";s:12:"default_name";s:20:"Armed Forces Pacific";}i:11;O:8:"stdClass":5:{s:2:"id";i:12;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"CA";s:12:"default_name";s:10:"California";}i:12;O:8:"stdClass":5:{s:2:"id";i:13;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"CO";s:12:"default_name";s:8:"Colorado";}i:13;O:8:"stdClass":5:{s:2:"id";i:14;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"CT";s:12:"default_name";s:11:"Connecticut";}i:14;O:8:"stdClass":5:{s:2:"id";i:15;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"DE";s:12:"default_name";s:8:"Delaware";}i:15;O:8:"stdClass":5:{s:2:"id";i:16;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"DC";s:12:"default_name";s:20:"District of Columbia";}i:16;O:8:"stdClass":5:{s:2:"id";i:17;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"FM";s:12:"default_name";s:30:"Federated States Of Micronesia";}i:17;O:8:"stdClass":5:{s:2:"id";i:18;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"FL";s:12:"default_name";s:7:"Florida";}i:18;O:8:"stdClass":5:{s:2:"id";i:19;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"GA";s:12:"default_name";s:7:"Georgia";}i:19;O:8:"stdClass":5:{s:2:"id";i:20;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"GU";s:12:"default_name";s:4:"Guam";}i:20;O:8:"stdClass":5:{s:2:"id";i:21;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"HI";s:12:"default_name";s:6:"Hawaii";}i:21;O:8:"stdClass":5:{s:2:"id";i:22;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"ID";s:12:"default_name";s:5:"Idaho";}i:22;O:8:"stdClass":5:{s:2:"id";i:23;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"IL";s:12:"default_name";s:8:"Illinois";}i:23;O:8:"stdClass":5:{s:2:"id";i:24;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"IN";s:12:"default_name";s:7:"Indiana";}i:24;O:8:"stdClass":5:{s:2:"id";i:25;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"IA";s:12:"default_name";s:4:"Iowa";}i:25;O:8:"stdClass":5:{s:2:"id";i:26;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"KS";s:12:"default_name";s:6:"Kansas";}i:26;O:8:"stdClass":5:{s:2:"id";i:27;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"KY";s:12:"default_name";s:8:"Kentucky";}i:27;O:8:"stdClass":5:{s:2:"id";i:28;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"LA";s:12:"default_name";s:9:"Louisiana";}i:28;O:8:"stdClass":5:{s:2:"id";i:29;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"ME";s:12:"default_name";s:5:"Maine";}i:29;O:8:"stdClass":5:{s:2:"id";i:30;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MH";s:12:"default_name";s:16:"Marshall Islands";}i:30;O:8:"stdClass":5:{s:2:"id";i:31;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MD";s:12:"default_name";s:8:"Maryland";}i:31;O:8:"stdClass":5:{s:2:"id";i:32;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MA";s:12:"default_name";s:13:"Massachusetts";}i:32;O:8:"stdClass":5:{s:2:"id";i:33;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MI";s:12:"default_name";s:8:"Michigan";}i:33;O:8:"stdClass":5:{s:2:"id";i:34;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MN";s:12:"default_name";s:9:"Minnesota";}i:34;O:8:"stdClass":5:{s:2:"id";i:35;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MS";s:12:"default_name";s:11:"Mississippi";}i:35;O:8:"stdClass":5:{s:2:"id";i:36;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MO";s:12:"default_name";s:8:"Missouri";}i:36;O:8:"stdClass":5:{s:2:"id";i:37;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MT";s:12:"default_name";s:7:"Montana";}i:37;O:8:"stdClass":5:{s:2:"id";i:38;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"NE";s:12:"default_name";s:8:"Nebraska";}i:38;O:8:"stdClass":5:{s:2:"id";i:39;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"NV";s:12:"default_name";s:6:"Nevada";}i:39;O:8:"stdClass":5:{s:2:"id";i:40;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"NH";s:12:"default_name";s:13:"New Hampshire";}i:40;O:8:"stdClass":5:{s:2:"id";i:41;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"NJ";s:12:"default_name";s:10:"New Jersey";}i:41;O:8:"stdClass":5:{s:2:"id";i:42;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"NM";s:12:"default_name";s:10:"New Mexico";}i:42;O:8:"stdClass":5:{s:2:"id";i:43;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"NY";s:12:"default_name";s:8:"New York";}i:43;O:8:"stdClass":5:{s:2:"id";i:44;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"NC";s:12:"default_name";s:14:"North Carolina";}i:44;O:8:"stdClass":5:{s:2:"id";i:45;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"ND";s:12:"default_name";s:12:"North Dakota";}i:45;O:8:"stdClass":5:{s:2:"id";i:46;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"MP";s:12:"default_name";s:24:"Northern Mariana Islands";}i:46;O:8:"stdClass":5:{s:2:"id";i:47;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"OH";s:12:"default_name";s:4:"Ohio";}i:47;O:8:"stdClass":5:{s:2:"id";i:48;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"OK";s:12:"default_name";s:8:"Oklahoma";}i:48;O:8:"stdClass":5:{s:2:"id";i:49;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"OR";s:12:"default_name";s:6:"Oregon";}i:49;O:8:"stdClass":5:{s:2:"id";i:50;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"PW";s:12:"default_name";s:5:"Palau";}i:50;O:8:"stdClass":5:{s:2:"id";i:51;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"PA";s:12:"default_name";s:12:"Pennsylvania";}i:51;O:8:"stdClass":5:{s:2:"id";i:52;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"PR";s:12:"default_name";s:11:"Puerto Rico";}i:52;O:8:"stdClass":5:{s:2:"id";i:53;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"RI";s:12:"default_name";s:12:"Rhode Island";}i:53;O:8:"stdClass":5:{s:2:"id";i:54;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"SC";s:12:"default_name";s:14:"South Carolina";}i:54;O:8:"stdClass":5:{s:2:"id";i:55;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"SD";s:12:"default_name";s:12:"South Dakota";}i:55;O:8:"stdClass":5:{s:2:"id";i:56;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"TN";s:12:"default_name";s:9:"Tennessee";}i:56;O:8:"stdClass":5:{s:2:"id";i:57;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"TX";s:12:"default_name";s:5:"Texas";}i:57;O:8:"stdClass":5:{s:2:"id";i:58;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"UT";s:12:"default_name";s:4:"Utah";}i:58;O:8:"stdClass":5:{s:2:"id";i:59;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"VT";s:12:"default_name";s:7:"Vermont";}i:59;O:8:"stdClass":5:{s:2:"id";i:60;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"VI";s:12:"default_name";s:14:"Virgin Islands";}i:60;O:8:"stdClass":5:{s:2:"id";i:61;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"VA";s:12:"default_name";s:8:"Virginia";}i:61;O:8:"stdClass":5:{s:2:"id";i:62;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"WA";s:12:"default_name";s:10:"Washington";}i:62;O:8:"stdClass":5:{s:2:"id";i:63;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"WV";s:12:"default_name";s:13:"West Virginia";}i:63;O:8:"stdClass":5:{s:2:"id";i:64;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"WI";s:12:"default_name";s:9:"Wisconsin";}i:64;O:8:"stdClass":5:{s:2:"id";i:65;s:10:"country_id";i:244;s:12:"country_code";s:2:"US";s:4:"code";s:2:"WY";s:12:"default_name";s:7:"Wyoming";}}s:28:" * escapeWhenCastingToString";b:0;}}}s:5:"label";s:28:"Tỉnh/Thành vận chuyển";}i:6;a:4:{s:3:"key";s:12:"cart|country";s:4:"type";s:6:"select";s:7:"options";a:254:{i:0;a:2:{s:2:"id";s:2:"AF";s:10:"admin_name";s:11:"Afghanistan";}i:1;a:2:{s:2:"id";s:2:"AX";s:10:"admin_name";s:14:"Åland Islands";}i:2;a:2:{s:2:"id";s:2:"AL";s:10:"admin_name";s:7:"Albania";}i:3;a:2:{s:2:"id";s:2:"DZ";s:10:"admin_name";s:7:"Algeria";}i:4;a:2:{s:2:"id";s:2:"AS";s:10:"admin_name";s:14:"American Samoa";}i:5;a:2:{s:2:"id";s:2:"AD";s:10:"admin_name";s:7:"Andorra";}i:6;a:2:{s:2:"id";s:2:"AO";s:10:"admin_name";s:6:"Angola";}i:7;a:2:{s:2:"id";s:2:"AI";s:10:"admin_name";s:8:"Anguilla";}i:8;a:2:{s:2:"id";s:2:"AQ";s:10:"admin_name";s:10:"Antarctica";}i:9;a:2:{s:2:"id";s:2:"AG";s:10:"admin_name";s:17:"Antigua & Barbuda";}i:10;a:2:{s:2:"id";s:2:"AR";s:10:"admin_name";s:9:"Argentina";}i:11;a:2:{s:2:"id";s:2:"AM";s:10:"admin_name";s:7:"Armenia";}i:12;a:2:{s:2:"id";s:2:"AW";s:10:"admin_name";s:5:"Aruba";}i:13;a:2:{s:2:"id";s:2:"AC";s:10:"admin_name";s:16:"Ascension Island";}i:14;a:2:{s:2:"id";s:2:"AU";s:10:"admin_name";s:9:"Australia";}i:15;a:2:{s:2:"id";s:2:"AT";s:10:"admin_name";s:7:"Austria";}i:16;a:2:{s:2:"id";s:2:"AZ";s:10:"admin_name";s:10:"Azerbaijan";}i:17;a:2:{s:2:"id";s:2:"BS";s:10:"admin_name";s:7:"Bahamas";}i:18;a:2:{s:2:"id";s:2:"BH";s:10:"admin_name";s:7:"Bahrain";}i:19;a:2:{s:2:"id";s:2:"BD";s:10:"admin_name";s:10:"Bangladesh";}i:20;a:2:{s:2:"id";s:2:"BB";s:10:"admin_name";s:8:"Barbados";}i:21;a:2:{s:2:"id";s:2:"BY";s:10:"admin_name";s:7:"Belarus";}i:22;a:2:{s:2:"id";s:2:"BE";s:10:"admin_name";s:7:"Belgium";}i:23;a:2:{s:2:"id";s:2:"BZ";s:10:"admin_name";s:6:"Belize";}i:24;a:2:{s:2:"id";s:2:"BJ";s:10:"admin_name";s:5:"Benin";}i:25;a:2:{s:2:"id";s:2:"BM";s:10:"admin_name";s:7:"Bermuda";}i:26;a:2:{s:2:"id";s:2:"BT";s:10:"admin_name";s:6:"Bhutan";}i:27;a:2:{s:2:"id";s:2:"BO";s:10:"admin_name";s:7:"Bolivia";}i:28;a:2:{s:2:"id";s:2:"BA";s:10:"admin_name";s:20:"Bosnia & Herzegovina";}i:29;a:2:{s:2:"id";s:2:"BW";s:10:"admin_name";s:8:"Botswana";}i:30;a:2:{s:2:"id";s:2:"BR";s:10:"admin_name";s:6:"Brazil";}i:31;a:2:{s:2:"id";s:2:"IO";s:10:"admin_name";s:30:"British Indian Ocean Territory";}i:32;a:2:{s:2:"id";s:2:"VG";s:10:"admin_name";s:22:"British Virgin Islands";}i:33;a:2:{s:2:"id";s:2:"BN";s:10:"admin_name";s:6:"Brunei";}i:34;a:2:{s:2:"id";s:2:"BG";s:10:"admin_name";s:8:"Bulgaria";}i:35;a:2:{s:2:"id";s:2:"BF";s:10:"admin_name";s:12:"Burkina Faso";}i:36;a:2:{s:2:"id";s:2:"BI";s:10:"admin_name";s:7:"Burundi";}i:37;a:2:{s:2:"id";s:2:"KH";s:10:"admin_name";s:8:"Cambodia";}i:38;a:2:{s:2:"id";s:2:"CM";s:10:"admin_name";s:8:"Cameroon";}i:39;a:2:{s:2:"id";s:2:"CA";s:10:"admin_name";s:6:"Canada";}i:40;a:2:{s:2:"id";s:2:"IC";s:10:"admin_name";s:14:"Canary Islands";}i:41;a:2:{s:2:"id";s:2:"CV";s:10:"admin_name";s:10:"Cape Verde";}i:42;a:2:{s:2:"id";s:2:"BQ";s:10:"admin_name";s:21:"Caribbean Netherlands";}i:43;a:2:{s:2:"id";s:2:"KY";s:10:"admin_name";s:14:"Cayman Islands";}i:44;a:2:{s:2:"id";s:2:"CF";s:10:"admin_name";s:24:"Central African Republic";}i:45;a:2:{s:2:"id";s:2:"EA";s:10:"admin_name";s:15:"Ceuta & Melilla";}i:46;a:2:{s:2:"id";s:2:"TD";s:10:"admin_name";s:4:"Chad";}i:47;a:2:{s:2:"id";s:2:"CL";s:10:"admin_name";s:5:"Chile";}i:48;a:2:{s:2:"id";s:2:"CN";s:10:"admin_name";s:5:"China";}i:49;a:2:{s:2:"id";s:2:"CX";s:10:"admin_name";s:16:"Christmas Island";}i:50;a:2:{s:2:"id";s:2:"CC";s:10:"admin_name";s:23:"Cocos (Keeling) Islands";}i:51;a:2:{s:2:"id";s:2:"CO";s:10:"admin_name";s:8:"Colombia";}i:52;a:2:{s:2:"id";s:2:"KM";s:10:"admin_name";s:7:"Comoros";}i:53;a:2:{s:2:"id";s:2:"CG";s:10:"admin_name";s:19:"Congo - Brazzaville";}i:54;a:2:{s:2:"id";s:2:"CD";s:10:"admin_name";s:16:"Congo - Kinshasa";}i:55;a:2:{s:2:"id";s:2:"CK";s:10:"admin_name";s:12:"Cook Islands";}i:56;a:2:{s:2:"id";s:2:"CR";s:10:"admin_name";s:10:"Costa Rica";}i:57;a:2:{s:2:"id";s:2:"CI";s:10:"admin_name";s:16:"Côte d’Ivoire";}i:58;a:2:{s:2:"id";s:2:"HR";s:10:"admin_name";s:7:"Croatia";}i:59;a:2:{s:2:"id";s:2:"CU";s:10:"admin_name";s:4:"Cuba";}i:60;a:2:{s:2:"id";s:2:"CW";s:10:"admin_name";s:8:"Curaçao";}i:61;a:2:{s:2:"id";s:2:"CY";s:10:"admin_name";s:6:"Cyprus";}i:62;a:2:{s:2:"id";s:2:"CZ";s:10:"admin_name";s:7:"Czechia";}i:63;a:2:{s:2:"id";s:2:"DK";s:10:"admin_name";s:7:"Denmark";}i:64;a:2:{s:2:"id";s:2:"DG";s:10:"admin_name";s:12:"Diego Garcia";}i:65;a:2:{s:2:"id";s:2:"DJ";s:10:"admin_name";s:8:"Djibouti";}i:66;a:2:{s:2:"id";s:2:"DM";s:10:"admin_name";s:8:"Dominica";}i:67;a:2:{s:2:"id";s:2:"DO";s:10:"admin_name";s:18:"Dominican Republic";}i:68;a:2:{s:2:"id";s:2:"EC";s:10:"admin_name";s:7:"Ecuador";}i:69;a:2:{s:2:"id";s:2:"EG";s:10:"admin_name";s:5:"Egypt";}i:70;a:2:{s:2:"id";s:2:"SV";s:10:"admin_name";s:11:"El Salvador";}i:71;a:2:{s:2:"id";s:2:"GQ";s:10:"admin_name";s:17:"Equatorial Guinea";}i:72;a:2:{s:2:"id";s:2:"ER";s:10:"admin_name";s:7:"Eritrea";}i:73;a:2:{s:2:"id";s:2:"EE";s:10:"admin_name";s:7:"Estonia";}i:74;a:2:{s:2:"id";s:2:"ET";s:10:"admin_name";s:8:"Ethiopia";}i:75;a:2:{s:2:"id";s:2:"EZ";s:10:"admin_name";s:8:"Eurozone";}i:76;a:2:{s:2:"id";s:2:"FK";s:10:"admin_name";s:16:"Falkland Islands";}i:77;a:2:{s:2:"id";s:2:"FO";s:10:"admin_name";s:13:"Faroe Islands";}i:78;a:2:{s:2:"id";s:2:"FJ";s:10:"admin_name";s:4:"Fiji";}i:79;a:2:{s:2:"id";s:2:"FI";s:10:"admin_name";s:7:"Finland";}i:80;a:2:{s:2:"id";s:2:"FR";s:10:"admin_name";s:6:"France";}i:81;a:2:{s:2:"id";s:2:"GF";s:10:"admin_name";s:13:"French Guiana";}i:82;a:2:{s:2:"id";s:2:"PF";s:10:"admin_name";s:16:"French Polynesia";}i:83;a:2:{s:2:"id";s:2:"TF";s:10:"admin_name";s:27:"French Southern Territories";}i:84;a:2:{s:2:"id";s:2:"GA";s:10:"admin_name";s:5:"Gabon";}i:85;a:2:{s:2:"id";s:2:"GM";s:10:"admin_name";s:6:"Gambia";}i:86;a:2:{s:2:"id";s:2:"GE";s:10:"admin_name";s:7:"Georgia";}i:87;a:2:{s:2:"id";s:2:"DE";s:10:"admin_name";s:7:"Germany";}i:88;a:2:{s:2:"id";s:2:"GH";s:10:"admin_name";s:5:"Ghana";}i:89;a:2:{s:2:"id";s:2:"GI";s:10:"admin_name";s:9:"Gibraltar";}i:90;a:2:{s:2:"id";s:2:"GR";s:10:"admin_name";s:6:"Greece";}i:91;a:2:{s:2:"id";s:2:"GL";s:10:"admin_name";s:9:"Greenland";}i:92;a:2:{s:2:"id";s:2:"GD";s:10:"admin_name";s:7:"Grenada";}i:93;a:2:{s:2:"id";s:2:"GP";s:10:"admin_name";s:10:"Guadeloupe";}i:94;a:2:{s:2:"id";s:2:"GU";s:10:"admin_name";s:4:"Guam";}i:95;a:2:{s:2:"id";s:2:"GT";s:10:"admin_name";s:9:"Guatemala";}i:96;a:2:{s:2:"id";s:2:"GG";s:10:"admin_name";s:8:"Guernsey";}i:97;a:2:{s:2:"id";s:2:"GN";s:10:"admin_name";s:6:"Guinea";}i:98;a:2:{s:2:"id";s:2:"GW";s:10:"admin_name";s:13:"Guinea-Bissau";}i:99;a:2:{s:2:"id";s:2:"GY";s:10:"admin_name";s:6:"Guyana";}i:100;a:2:{s:2:"id";s:2:"HT";s:10:"admin_name";s:5:"Haiti";}i:101;a:2:{s:2:"id";s:2:"HN";s:10:"admin_name";s:8:"Honduras";}i:102;a:2:{s:2:"id";s:2:"HK";s:10:"admin_name";s:19:"Hong Kong SAR China";}i:103;a:2:{s:2:"id";s:2:"HU";s:10:"admin_name";s:7:"Hungary";}i:104;a:2:{s:2:"id";s:2:"IS";s:10:"admin_name";s:7:"Iceland";}i:105;a:2:{s:2:"id";s:2:"IN";s:10:"admin_name";s:5:"India";}i:106;a:2:{s:2:"id";s:2:"ID";s:10:"admin_name";s:9:"Indonesia";}i:107;a:2:{s:2:"id";s:2:"IR";s:10:"admin_name";s:4:"Iran";}i:108;a:2:{s:2:"id";s:2:"IQ";s:10:"admin_name";s:4:"Iraq";}i:109;a:2:{s:2:"id";s:2:"IE";s:10:"admin_name";s:7:"Ireland";}i:110;a:2:{s:2:"id";s:2:"IM";s:10:"admin_name";s:11:"Isle of Man";}i:111;a:2:{s:2:"id";s:2:"IL";s:10:"admin_name";s:6:"Israel";}i:112;a:2:{s:2:"id";s:2:"IT";s:10:"admin_name";s:5:"Italy";}i:113;a:2:{s:2:"id";s:2:"JM";s:10:"admin_name";s:7:"Jamaica";}i:114;a:2:{s:2:"id";s:2:"JP";s:10:"admin_name";s:5:"Japan";}i:115;a:2:{s:2:"id";s:2:"JE";s:10:"admin_name";s:6:"Jersey";}i:116;a:2:{s:2:"id";s:2:"JO";s:10:"admin_name";s:6:"Jordan";}i:117;a:2:{s:2:"id";s:2:"KZ";s:10:"admin_name";s:10:"Kazakhstan";}i:118;a:2:{s:2:"id";s:2:"KE";s:10:"admin_name";s:5:"Kenya";}i:119;a:2:{s:2:"id";s:2:"KI";s:10:"admin_name";s:8:"Kiribati";}i:120;a:2:{s:2:"id";s:2:"XK";s:10:"admin_name";s:6:"Kosovo";}i:121;a:2:{s:2:"id";s:2:"KW";s:10:"admin_name";s:6:"Kuwait";}i:122;a:2:{s:2:"id";s:2:"KG";s:10:"admin_name";s:10:"Kyrgyzstan";}i:123;a:2:{s:2:"id";s:2:"LA";s:10:"admin_name";s:4:"Laos";}i:124;a:2:{s:2:"id";s:2:"LV";s:10:"admin_name";s:6:"Latvia";}i:125;a:2:{s:2:"id";s:2:"LB";s:10:"admin_name";s:7:"Lebanon";}i:126;a:2:{s:2:"id";s:2:"LS";s:10:"admin_name";s:7:"Lesotho";}i:127;a:2:{s:2:"id";s:2:"LR";s:10:"admin_name";s:7:"Liberia";}i:128;a:2:{s:2:"id";s:2:"LY";s:10:"admin_name";s:5:"Libya";}i:129;a:2:{s:2:"id";s:2:"LI";s:10:"admin_name";s:13:"Liechtenstein";}i:130;a:2:{s:2:"id";s:2:"LT";s:10:"admin_name";s:9:"Lithuania";}i:131;a:2:{s:2:"id";s:2:"LU";s:10:"admin_name";s:10:"Luxembourg";}i:132;a:2:{s:2:"id";s:2:"MO";s:10:"admin_name";s:15:"Macau SAR China";}i:133;a:2:{s:2:"id";s:2:"MK";s:10:"admin_name";s:9:"Macedonia";}i:134;a:2:{s:2:"id";s:2:"MG";s:10:"admin_name";s:10:"Madagascar";}i:135;a:2:{s:2:"id";s:2:"MW";s:10:"admin_name";s:6:"Malawi";}i:136;a:2:{s:2:"id";s:2:"MY";s:10:"admin_name";s:8:"Malaysia";}i:137;a:2:{s:2:"id";s:2:"MV";s:10:"admin_name";s:8:"Maldives";}i:138;a:2:{s:2:"id";s:2:"ML";s:10:"admin_name";s:4:"Mali";}i:139;a:2:{s:2:"id";s:2:"MT";s:10:"admin_name";s:5:"Malta";}i:140;a:2:{s:2:"id";s:2:"MH";s:10:"admin_name";s:16:"Marshall Islands";}i:141;a:2:{s:2:"id";s:2:"MQ";s:10:"admin_name";s:10:"Martinique";}i:142;a:2:{s:2:"id";s:2:"MR";s:10:"admin_name";s:10:"Mauritania";}i:143;a:2:{s:2:"id";s:2:"MU";s:10:"admin_name";s:9:"Mauritius";}i:144;a:2:{s:2:"id";s:2:"YT";s:10:"admin_name";s:7:"Mayotte";}i:145;a:2:{s:2:"id";s:2:"MX";s:10:"admin_name";s:6:"Mexico";}i:146;a:2:{s:2:"id";s:2:"FM";s:10:"admin_name";s:10:"Micronesia";}i:147;a:2:{s:2:"id";s:2:"MD";s:10:"admin_name";s:7:"Moldova";}i:148;a:2:{s:2:"id";s:2:"MC";s:10:"admin_name";s:6:"Monaco";}i:149;a:2:{s:2:"id";s:2:"MN";s:10:"admin_name";s:8:"Mongolia";}i:150;a:2:{s:2:"id";s:2:"ME";s:10:"admin_name";s:10:"Montenegro";}i:151;a:2:{s:2:"id";s:2:"MS";s:10:"admin_name";s:10:"Montserrat";}i:152;a:2:{s:2:"id";s:2:"MA";s:10:"admin_name";s:7:"Morocco";}i:153;a:2:{s:2:"id";s:2:"MZ";s:10:"admin_name";s:10:"Mozambique";}i:154;a:2:{s:2:"id";s:2:"MM";s:10:"admin_name";s:15:"Myanmar (Burma)";}i:155;a:2:{s:2:"id";s:2:"NA";s:10:"admin_name";s:7:"Namibia";}i:156;a:2:{s:2:"id";s:2:"NR";s:10:"admin_name";s:5:"Nauru";}i:157;a:2:{s:2:"id";s:2:"NP";s:10:"admin_name";s:5:"Nepal";}i:158;a:2:{s:2:"id";s:2:"NL";s:10:"admin_name";s:11:"Netherlands";}i:159;a:2:{s:2:"id";s:2:"NC";s:10:"admin_name";s:13:"New Caledonia";}i:160;a:2:{s:2:"id";s:2:"NZ";s:10:"admin_name";s:11:"New Zealand";}i:161;a:2:{s:2:"id";s:2:"NI";s:10:"admin_name";s:9:"Nicaragua";}i:162;a:2:{s:2:"id";s:2:"NE";s:10:"admin_name";s:5:"Niger";}i:163;a:2:{s:2:"id";s:2:"NG";s:10:"admin_name";s:7:"Nigeria";}i:164;a:2:{s:2:"id";s:2:"NU";s:10:"admin_name";s:4:"Niue";}i:165;a:2:{s:2:"id";s:2:"NF";s:10:"admin_name";s:14:"Norfolk Island";}i:166;a:2:{s:2:"id";s:2:"KP";s:10:"admin_name";s:11:"North Korea";}i:167;a:2:{s:2:"id";s:2:"MP";s:10:"admin_name";s:24:"Northern Mariana Islands";}i:168;a:2:{s:2:"id";s:2:"NO";s:10:"admin_name";s:6:"Norway";}i:169;a:2:{s:2:"id";s:2:"OM";s:10:"admin_name";s:4:"Oman";}i:170;a:2:{s:2:"id";s:2:"PK";s:10:"admin_name";s:8:"Pakistan";}i:171;a:2:{s:2:"id";s:2:"PW";s:10:"admin_name";s:5:"Palau";}i:172;a:2:{s:2:"id";s:2:"PS";s:10:"admin_name";s:23:"Palestinian Territories";}i:173;a:2:{s:2:"id";s:2:"PA";s:10:"admin_name";s:6:"Panama";}i:174;a:2:{s:2:"id";s:2:"PG";s:10:"admin_name";s:16:"Papua New Guinea";}i:175;a:2:{s:2:"id";s:2:"PY";s:10:"admin_name";s:8:"Paraguay";}i:176;a:2:{s:2:"id";s:2:"PE";s:10:"admin_name";s:4:"Peru";}i:177;a:2:{s:2:"id";s:2:"PH";s:10:"admin_name";s:11:"Philippines";}i:178;a:2:{s:2:"id";s:2:"PN";s:10:"admin_name";s:16:"Pitcairn Islands";}i:179;a:2:{s:2:"id";s:2:"PL";s:10:"admin_name";s:6:"Poland";}i:180;a:2:{s:2:"id";s:2:"PT";s:10:"admin_name";s:8:"Portugal";}i:181;a:2:{s:2:"id";s:2:"PR";s:10:"admin_name";s:11:"Puerto Rico";}i:182;a:2:{s:2:"id";s:2:"QA";s:10:"admin_name";s:5:"Qatar";}i:183;a:2:{s:2:"id";s:2:"RE";s:10:"admin_name";s:8:"Réunion";}i:184;a:2:{s:2:"id";s:2:"RO";s:10:"admin_name";s:7:"Romania";}i:185;a:2:{s:2:"id";s:2:"RU";s:10:"admin_name";s:6:"Russia";}i:186;a:2:{s:2:"id";s:2:"RW";s:10:"admin_name";s:6:"Rwanda";}i:187;a:2:{s:2:"id";s:2:"WS";s:10:"admin_name";s:5:"Samoa";}i:188;a:2:{s:2:"id";s:2:"SM";s:10:"admin_name";s:10:"San Marino";}i:189;a:2:{s:2:"id";s:2:"ST";s:10:"admin_name";s:22:"São Tomé & Príncipe";}i:190;a:2:{s:2:"id";s:2:"SA";s:10:"admin_name";s:12:"Saudi Arabia";}i:191;a:2:{s:2:"id";s:2:"SN";s:10:"admin_name";s:7:"Senegal";}i:192;a:2:{s:2:"id";s:2:"RS";s:10:"admin_name";s:6:"Serbia";}i:193;a:2:{s:2:"id";s:2:"SC";s:10:"admin_name";s:10:"Seychelles";}i:194;a:2:{s:2:"id";s:2:"SL";s:10:"admin_name";s:12:"Sierra Leone";}i:195;a:2:{s:2:"id";s:2:"SG";s:10:"admin_name";s:9:"Singapore";}i:196;a:2:{s:2:"id";s:2:"SX";s:10:"admin_name";s:12:"Sint Maarten";}i:197;a:2:{s:2:"id";s:2:"SK";s:10:"admin_name";s:8:"Slovakia";}i:198;a:2:{s:2:"id";s:2:"SI";s:10:"admin_name";s:8:"Slovenia";}i:199;a:2:{s:2:"id";s:2:"SB";s:10:"admin_name";s:15:"Solomon Islands";}i:200;a:2:{s:2:"id";s:2:"SO";s:10:"admin_name";s:7:"Somalia";}i:201;a:2:{s:2:"id";s:2:"ZA";s:10:"admin_name";s:12:"South Africa";}i:202;a:2:{s:2:"id";s:2:"GS";s:10:"admin_name";s:38:"South Georgia & South Sandwich Islands";}i:203;a:2:{s:2:"id";s:2:"KR";s:10:"admin_name";s:11:"South Korea";}i:204;a:2:{s:2:"id";s:2:"SS";s:10:"admin_name";s:11:"South Sudan";}i:205;a:2:{s:2:"id";s:2:"ES";s:10:"admin_name";s:5:"Spain";}i:206;a:2:{s:2:"id";s:2:"LK";s:10:"admin_name";s:9:"Sri Lanka";}i:207;a:2:{s:2:"id";s:2:"BL";s:10:"admin_name";s:15:"St. Barthélemy";}i:208;a:2:{s:2:"id";s:2:"SH";s:10:"admin_name";s:10:"St. Helena";}i:209;a:2:{s:2:"id";s:2:"KN";s:10:"admin_name";s:17:"St. Kitts & Nevis";}i:210;a:2:{s:2:"id";s:2:"LC";s:10:"admin_name";s:9:"St. Lucia";}i:211;a:2:{s:2:"id";s:2:"MF";s:10:"admin_name";s:10:"St. Martin";}i:212;a:2:{s:2:"id";s:2:"PM";s:10:"admin_name";s:21:"St. Pierre & Miquelon";}i:213;a:2:{s:2:"id";s:2:"VC";s:10:"admin_name";s:24:"St. Vincent & Grenadines";}i:214;a:2:{s:2:"id";s:2:"SD";s:10:"admin_name";s:5:"Sudan";}i:215;a:2:{s:2:"id";s:2:"SR";s:10:"admin_name";s:8:"Suriname";}i:216;a:2:{s:2:"id";s:2:"SJ";s:10:"admin_name";s:20:"Svalbard & Jan Mayen";}i:217;a:2:{s:2:"id";s:2:"SZ";s:10:"admin_name";s:9:"Swaziland";}i:218;a:2:{s:2:"id";s:2:"SE";s:10:"admin_name";s:6:"Sweden";}i:219;a:2:{s:2:"id";s:2:"CH";s:10:"admin_name";s:11:"Switzerland";}i:220;a:2:{s:2:"id";s:2:"SY";s:10:"admin_name";s:5:"Syria";}i:221;a:2:{s:2:"id";s:2:"TW";s:10:"admin_name";s:6:"Taiwan";}i:222;a:2:{s:2:"id";s:2:"TJ";s:10:"admin_name";s:10:"Tajikistan";}i:223;a:2:{s:2:"id";s:2:"TZ";s:10:"admin_name";s:8:"Tanzania";}i:224;a:2:{s:2:"id";s:2:"TH";s:10:"admin_name";s:8:"Thailand";}i:225;a:2:{s:2:"id";s:2:"TL";s:10:"admin_name";s:11:"Timor-Leste";}i:226;a:2:{s:2:"id";s:2:"TG";s:10:"admin_name";s:4:"Togo";}i:227;a:2:{s:2:"id";s:2:"TK";s:10:"admin_name";s:7:"Tokelau";}i:228;a:2:{s:2:"id";s:2:"TO";s:10:"admin_name";s:5:"Tonga";}i:229;a:2:{s:2:"id";s:2:"TT";s:10:"admin_name";s:17:"Trinidad & Tobago";}i:230;a:2:{s:2:"id";s:2:"TA";s:10:"admin_name";s:16:"Tristan da Cunha";}i:231;a:2:{s:2:"id";s:2:"TN";s:10:"admin_name";s:7:"Tunisia";}i:232;a:2:{s:2:"id";s:2:"TR";s:10:"admin_name";s:6:"Turkey";}i:233;a:2:{s:2:"id";s:2:"TM";s:10:"admin_name";s:12:"Turkmenistan";}i:234;a:2:{s:2:"id";s:2:"TC";s:10:"admin_name";s:22:"Turks & Caicos Islands";}i:235;a:2:{s:2:"id";s:2:"TV";s:10:"admin_name";s:6:"Tuvalu";}i:236;a:2:{s:2:"id";s:2:"UM";s:10:"admin_name";s:21:"U.S. Outlying Islands";}i:237;a:2:{s:2:"id";s:2:"VI";s:10:"admin_name";s:19:"U.S. Virgin Islands";}i:238;a:2:{s:2:"id";s:2:"UG";s:10:"admin_name";s:6:"Uganda";}i:239;a:2:{s:2:"id";s:2:"UA";s:10:"admin_name";s:7:"Ukraine";}i:240;a:2:{s:2:"id";s:2:"AE";s:10:"admin_name";s:20:"United Arab Emirates";}i:241;a:2:{s:2:"id";s:2:"GB";s:10:"admin_name";s:14:"United Kingdom";}i:242;a:2:{s:2:"id";s:2:"US";s:10:"admin_name";s:13:"United States";}i:243;a:2:{s:2:"id";s:2:"UY";s:10:"admin_name";s:7:"Uruguay";}i:244;a:2:{s:2:"id";s:2:"UZ";s:10:"admin_name";s:10:"Uzbekistan";}i:245;a:2:{s:2:"id";s:2:"VU";s:10:"admin_name";s:7:"Vanuatu";}i:246;a:2:{s:2:"id";s:2:"VA";s:10:"admin_name";s:12:"Vatican City";}i:247;a:2:{s:2:"id";s:2:"VE";s:10:"admin_name";s:9:"Venezuela";}i:248;a:2:{s:2:"id";s:2:"VN";s:10:"admin_name";s:7:"Vietnam";}i:249;a:2:{s:2:"id";s:2:"WF";s:10:"admin_name";s:15:"Wallis & Futuna";}i:250;a:2:{s:2:"id";s:2:"EH";s:10:"admin_name";s:14:"Western Sahara";}i:251;a:2:{s:2:"id";s:2:"YE";s:10:"admin_name";s:5:"Yemen";}i:252;a:2:{s:2:"id";s:2:"ZM";s:10:"admin_name";s:6:"Zambia";}i:253;a:2:{s:2:"id";s:2:"ZW";s:10:"admin_name";s:8:"Zimbabwe";}}s:5:"label";s:25:"Quốc gia vận chuyển";}}}i:1;a:3:{s:3:"key";s:9:"cart_item";s:5:"label";s:37:"Thuộc tính mặt hàng giỏ hàng";s:8:"children";a:5:{i:0;a:3:{s:3:"key";s:20:"cart_item|base_price";s:4:"type";s:5:"price";s:5:"label";s:22:"Giá trong giỏ hàng";}i:1;a:3:{s:3:"key";s:18:"cart_item|quantity";s:4:"type";s:7:"integer";s:5:"label";s:31:"Số lượng trong giỏ hàng";}i:2;a:3:{s:3:"key";s:27:"cart_item|base_total_weight";s:4:"type";s:7:"decimal";s:5:"label";s:23:"Tổng trọng lượng";}i:3;a:3:{s:3:"key";s:20:"cart_item|base_total";s:4:"type";s:5:"price";s:5:"label";s:12:"Tổng phụ";}i:4;a:3:{s:3:"key";s:20:"cart_item|additional";s:4:"type";s:4:"text";s:5:"label";s:9:"Bổ sung";}}}i:2;a:3:{s:3:"key";s:7:"product";s:5:"label";s:26:"Thuộc tính sản phẩm";s:8:"children";a:97:{i:0;a:5:{s:3:"key";s:20:"product|category_ids";s:4:"type";s:11:"multiselect";s:5:"label";s:10:"Danh mục";s:7:"options";a:0:{}s:9:"lazy_load";b:1;}i:1;a:5:{s:3:"key";s:30:"product|children::category_ids";s:4:"type";s:11:"multiselect";s:5:"label";s:22:"Danh mục (chỉ con)";s:7:"options";a:0:{}s:9:"lazy_load";b:1;}i:2;a:5:{s:3:"key";s:28:"product|parent::category_ids";s:4:"type";s:11:"multiselect";s:5:"label";s:22:"Danh mục (chỉ cha)";s:7:"options";a:0:{}s:9:"lazy_load";b:1;}i:3;a:4:{s:3:"key";s:27:"product|attribute_family_id";s:4:"type";s:6:"select";s:5:"label";s:18:"Họ thuộc tính";s:7:"options";a:1:{i:0;a:2:{s:2:"id";i:1;s:10:"admin_name";s:7:"Default";}}}i:4;a:5:{s:3:"key";s:11:"product|sku";s:4:"type";s:4:"text";s:5:"label";s:3:"SKU";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:5;a:5:{s:3:"key";s:21:"product|children::sku";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:6;a:5:{s:3:"key";s:19:"product|parent::sku";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:7;a:5:{s:3:"key";s:12:"product|name";s:4:"type";s:4:"text";s:5:"label";s:4:"Name";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:8;a:5:{s:3:"key";s:22:"product|children::name";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:9;a:5:{s:3:"key";s:20:"product|parent::name";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:10;a:5:{s:3:"key";s:15:"product|url_key";s:4:"type";s:4:"text";s:5:"label";s:7:"URL Key";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:11;a:5:{s:3:"key";s:25:"product|children::url_key";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:12;a:5:{s:3:"key";s:23:"product|parent::url_key";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:13;a:5:{s:3:"key";s:23:"product|tax_category_id";s:4:"type";s:6:"select";s:5:"label";s:12:"Tax Category";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:14;a:5:{s:3:"key";s:33:"product|children::tax_category_id";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:15;a:5:{s:3:"key";s:31:"product|parent::tax_category_id";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:16;a:5:{s:3:"key";s:11:"product|new";s:4:"type";s:7:"boolean";s:5:"label";s:3:"New";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:17;a:5:{s:3:"key";s:21:"product|children::new";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:18;a:5:{s:3:"key";s:19:"product|parent::new";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:19;a:5:{s:3:"key";s:16:"product|featured";s:4:"type";s:7:"boolean";s:5:"label";s:8:"Featured";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:20;a:5:{s:3:"key";s:26:"product|children::featured";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:21;a:5:{s:3:"key";s:24:"product|parent::featured";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:22;a:5:{s:3:"key";s:28:"product|visible_individually";s:4:"type";s:7:"boolean";s:5:"label";s:20:"Visible Individually";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:23;a:5:{s:3:"key";s:38:"product|children::visible_individually";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:24;a:5:{s:3:"key";s:36:"product|parent::visible_individually";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:25;a:5:{s:3:"key";s:14:"product|status";s:4:"type";s:7:"boolean";s:5:"label";s:6:"Status";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:26;a:5:{s:3:"key";s:24:"product|children::status";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:27;a:5:{s:3:"key";s:22:"product|parent::status";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:28;a:5:{s:3:"key";s:13:"product|price";s:4:"type";s:5:"price";s:5:"label";s:5:"Price";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:29;a:5:{s:3:"key";s:23:"product|children::price";s:4:"type";s:5:"price";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:30;a:5:{s:3:"key";s:21:"product|parent::price";s:4:"type";s:5:"price";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:31;a:5:{s:3:"key";s:12:"product|cost";s:4:"type";s:5:"price";s:5:"label";s:4:"Cost";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:32;a:5:{s:3:"key";s:22:"product|children::cost";s:4:"type";s:5:"price";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:33;a:5:{s:3:"key";s:20:"product|parent::cost";s:4:"type";s:5:"price";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:34;a:5:{s:3:"key";s:21:"product|special_price";s:4:"type";s:5:"price";s:5:"label";s:13:"Special Price";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:35;a:5:{s:3:"key";s:31:"product|children::special_price";s:4:"type";s:5:"price";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:36;a:5:{s:3:"key";s:29:"product|parent::special_price";s:4:"type";s:5:"price";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:37;a:5:{s:3:"key";s:26:"product|special_price_from";s:4:"type";s:4:"date";s:5:"label";s:18:"Special Price From";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:38;a:5:{s:3:"key";s:36:"product|children::special_price_from";s:4:"type";s:4:"date";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:39;a:5:{s:3:"key";s:34:"product|parent::special_price_from";s:4:"type";s:4:"date";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:40;a:5:{s:3:"key";s:24:"product|special_price_to";s:4:"type";s:4:"date";s:5:"label";s:16:"Special Price To";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:41;a:5:{s:3:"key";s:34:"product|children::special_price_to";s:4:"type";s:4:"date";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:42;a:5:{s:3:"key";s:32:"product|parent::special_price_to";s:4:"type";s:4:"date";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:43;a:5:{s:3:"key";s:14:"product|length";s:4:"type";s:4:"text";s:5:"label";s:6:"Length";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:44;a:5:{s:3:"key";s:24:"product|children::length";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:45;a:5:{s:3:"key";s:22:"product|parent::length";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:46;a:5:{s:3:"key";s:13:"product|width";s:4:"type";s:4:"text";s:5:"label";s:5:"Width";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:47;a:5:{s:3:"key";s:23:"product|children::width";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:48;a:5:{s:3:"key";s:21:"product|parent::width";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:49;a:5:{s:3:"key";s:14:"product|height";s:4:"type";s:4:"text";s:5:"label";s:6:"Height";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:50;a:5:{s:3:"key";s:24:"product|children::height";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:51;a:5:{s:3:"key";s:22:"product|parent::height";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:52;a:5:{s:3:"key";s:14:"product|weight";s:4:"type";s:4:"text";s:5:"label";s:6:"Weight";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:53;a:5:{s:3:"key";s:24:"product|children::weight";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:54;a:5:{s:3:"key";s:22:"product|parent::weight";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:55;a:5:{s:3:"key";s:13:"product|color";s:4:"type";s:6:"select";s:5:"label";s:5:"Color";s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1;s:12:"attribute_id";i:23;s:10:"admin_name";s:3:"Red";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1;s:12:"attribute_id";i:23;s:10:"admin_name";s:3:"Red";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:1;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2;s:12:"attribute_id";i:23;s:10:"admin_name";s:5:"Green";s:10:"sort_order";i:2;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:2;s:12:"attribute_id";i:23;s:10:"admin_name";s:5:"Green";s:10:"sort_order";i:2;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:2;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:3;s:12:"attribute_id";i:23;s:10:"admin_name";s:6:"Yellow";s:10:"sort_order";i:3;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:3;s:12:"attribute_id";i:23;s:10:"admin_name";s:6:"Yellow";s:10:"sort_order";i:3;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:3;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:4;s:12:"attribute_id";i:23;s:10:"admin_name";s:5:"Black";s:10:"sort_order";i:4;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:4;s:12:"attribute_id";i:23;s:10:"admin_name";s:5:"Black";s:10:"sort_order";i:4;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:4;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:5;s:12:"attribute_id";i:23;s:10:"admin_name";s:5:"White";s:10:"sort_order";i:5;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:5;s:12:"attribute_id";i:23;s:10:"admin_name";s:5:"White";s:10:"sort_order";i:5;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}}s:28:" * escapeWhenCastingToString";b:0;}s:9:"lazy_load";b:0;}i:56;a:5:{s:3:"key";s:23:"product|children::color";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";r:4794;s:9:"lazy_load";b:0;}i:57;a:5:{s:3:"key";s:21:"product|parent::color";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";r:4794;s:9:"lazy_load";b:0;}i:58;a:5:{s:3:"key";s:12:"product|size";s:4:"type";s:6:"select";s:5:"label";s:4:"Size";s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:6;s:12:"attribute_id";i:24;s:10:"admin_name";s:1:"S";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:6;s:12:"attribute_id";i:24;s:10:"admin_name";s:1:"S";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:1;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:7;s:12:"attribute_id";i:24;s:10:"admin_name";s:1:"M";s:10:"sort_order";i:2;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:7;s:12:"attribute_id";i:24;s:10:"admin_name";s:1:"M";s:10:"sort_order";i:2;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:2;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:8;s:12:"attribute_id";i:24;s:10:"admin_name";s:1:"L";s:10:"sort_order";i:3;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:8;s:12:"attribute_id";i:24;s:10:"admin_name";s:1:"L";s:10:"sort_order";i:3;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:3;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:9;s:12:"attribute_id";i:24;s:10:"admin_name";s:2:"XL";s:10:"sort_order";i:4;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:9;s:12:"attribute_id";i:24;s:10:"admin_name";s:2:"XL";s:10:"sort_order";i:4;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}}s:28:" * escapeWhenCastingToString";b:0;}s:9:"lazy_load";b:0;}i:59;a:5:{s:3:"key";s:22:"product|children::size";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";r:5094;s:9:"lazy_load";b:0;}i:60;a:5:{s:3:"key";s:20:"product|parent::size";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";r:5094;s:9:"lazy_load";b:0;}i:61;a:5:{s:3:"key";s:22:"product|guest_checkout";s:4:"type";s:7:"boolean";s:5:"label";s:14:"Guest Checkout";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:62;a:5:{s:3:"key";s:32:"product|children::guest_checkout";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:63;a:5:{s:3:"key";s:30:"product|parent::guest_checkout";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:64;a:5:{s:3:"key";s:22:"product|product_number";s:4:"type";s:4:"text";s:5:"label";s:14:"Product Number";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:65;a:5:{s:3:"key";s:32:"product|children::product_number";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:66;a:5:{s:3:"key";s:30:"product|parent::product_number";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:67;a:5:{s:3:"key";s:20:"product|manage_stock";s:4:"type";s:7:"boolean";s:5:"label";s:12:"Manage Stock";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:68;a:5:{s:3:"key";s:30:"product|children::manage_stock";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:69;a:5:{s:3:"key";s:28:"product|parent::manage_stock";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:70;a:5:{s:3:"key";s:13:"product|brand";s:4:"type";s:6:"select";s:5:"label";s:5:"Brand";s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:50:{i:0;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:80;s:12:"attribute_id";i:43;s:10:"admin_name";s:19:"Merck Sharp & Dohme";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:80;s:12:"attribute_id";i:43;s:10:"admin_name";s:19:"Merck Sharp & Dohme";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:1;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:88;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Brauer";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:88;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Brauer";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:2;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:87;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"STELLA";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:87;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"STELLA";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:3;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:86;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Novartis";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:86;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Novartis";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:4;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:85;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Enzymax";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:85;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Enzymax";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:5;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:84;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Cetaphil";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:84;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Cetaphil";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:6;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:83;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Anessa";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:83;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Anessa";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:7;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:82;s:12:"attribute_id";i:43;s:10:"admin_name";s:13:"Alltimes Care";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:82;s:12:"attribute_id";i:43;s:10:"admin_name";s:13:"Alltimes Care";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:8;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:81;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"X Men";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:81;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"X Men";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:9;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:89;s:12:"attribute_id";i:43;s:10:"admin_name";s:11:"Embryolisse";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:89;s:12:"attribute_id";i:43;s:10:"admin_name";s:11:"Embryolisse";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:10;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:79;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Fixderma";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:79;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Fixderma";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:11;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:78;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Nucos";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:78;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Nucos";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:12;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:77;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Senka";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:77;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Senka";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:13;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:76;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Blackmores";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:76;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Blackmores";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:14;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:75;s:12:"attribute_id";i:43;s:10:"admin_name";s:2:"CJ";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:75;s:12:"attribute_id";i:43;s:10:"admin_name";s:2:"CJ";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:15;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:74;s:12:"attribute_id";i:43;s:10:"admin_name";s:17:"Mega Lifesciences";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:74;s:12:"attribute_id";i:43;s:10:"admin_name";s:17:"Mega Lifesciences";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:16;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:73;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Life Space";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:73;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Life Space";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:17;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:72;s:12:"attribute_id";i:43;s:10:"admin_name";s:11:"Kinohimitsu";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:72;s:12:"attribute_id";i:43;s:10:"admin_name";s:11:"Kinohimitsu";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:18;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:98;s:12:"attribute_id";i:43;s:10:"admin_name";s:12:"Swiss Energy";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:98;s:12:"attribute_id";i:43;s:10:"admin_name";s:12:"Swiss Energy";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:19;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2123;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Sagopha";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:2123;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Sagopha";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:20;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2122;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Omnican";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:2122;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Omnican";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:21;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2120;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"KACHI";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:2120;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"KACHI";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:22;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:103;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Johnson";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:103;s:12:"attribute_id";i:43;s:10:"admin_name";s:7:"Johnson";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:23;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:102;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Sanofi GEM";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:102;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Sanofi GEM";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:24;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:101;s:12:"attribute_id";i:43;s:10:"admin_name";s:11:"Vitabiotics";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:101;s:12:"attribute_id";i:43;s:10:"admin_name";s:11:"Vitabiotics";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:25;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:100;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Nutrigen";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:100;s:12:"attribute_id";i:43;s:10:"admin_name";s:8:"Nutrigen";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:26;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:99;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Welson";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:99;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Welson";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:27;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:71;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Listerine";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:71;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Listerine";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:28;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:97;s:12:"attribute_id";i:43;s:10:"admin_name";s:4:"Urgo";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:97;s:12:"attribute_id";i:43;s:10:"admin_name";s:4:"Urgo";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:29;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:96;s:12:"attribute_id";i:43;s:10:"admin_name";s:15:"GRAND NUTRITION";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:96;s:12:"attribute_id";i:43;s:10:"admin_name";s:15:"GRAND NUTRITION";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:30;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:95;s:12:"attribute_id";i:43;s:10:"admin_name";s:4:"Fino";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:95;s:12:"attribute_id";i:43;s:10:"admin_name";s:4:"Fino";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:31;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:94;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Durex";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:94;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Durex";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:32;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:93;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Solgar";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:93;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Solgar";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:33;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:92;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Microlife";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:92;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Microlife";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:34;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:91;s:12:"attribute_id";i:43;s:10:"admin_name";s:3:"Uno";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:91;s:12:"attribute_id";i:43;s:10:"admin_name";s:3:"Uno";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:35;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:90;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Nivea";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:90;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Nivea";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:36;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:44;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Imexpharm";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:44;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Imexpharm";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:37;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:52;s:12:"attribute_id";i:43;s:10:"admin_name";s:22:"Pharmacist Formulators";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:52;s:12:"attribute_id";i:43;s:10:"admin_name";s:22:"Pharmacist Formulators";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:38;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:51;s:12:"attribute_id";i:43;s:10:"admin_name";s:3:"82X";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:51;s:12:"attribute_id";i:43;s:10:"admin_name";s:3:"82X";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:39;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:50;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Efferalgan";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:50;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Efferalgan";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:40;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:49;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Abbott";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:49;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Abbott";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:41;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:48;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"PediaSure";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:48;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"PediaSure";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:42;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:47;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Rohto";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:47;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Rohto";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:43;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:46;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"SHINPOONG";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:46;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"SHINPOONG";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:44;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:45;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Tracy Bee";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:45;s:12:"attribute_id";i:43;s:10:"admin_name";s:9:"Tracy Bee";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:45;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:53;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Goodhealth";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:53;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Goodhealth";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:46;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:43;s:12:"attribute_id";i:43;s:10:"admin_name";s:12:"Henry Blooms";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:43;s:12:"attribute_id";i:43;s:10:"admin_name";s:12:"Henry Blooms";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:47;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:42;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Remos";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:42;s:12:"attribute_id";i:43;s:10:"admin_name";s:5:"Remos";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:48;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:41;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Swisse";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:41;s:12:"attribute_id";i:43;s:10:"admin_name";s:6:"Swisse";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:49;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:40;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Pharmacity";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:40;s:12:"attribute_id";i:43;s:10:"admin_name";s:10:"Pharmacity";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}}s:28:" * escapeWhenCastingToString";b:0;}s:9:"lazy_load";b:1;}i:71;a:5:{s:3:"key";s:23:"product|children::brand";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";r:5392;s:9:"lazy_load";b:1;}i:72;a:5:{s:3:"key";s:21:"product|parent::brand";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";r:5392;s:9:"lazy_load";b:1;}i:73;a:5:{s:3:"key";s:25:"product|active_ingredient";s:4:"type";s:6:"select";s:5:"label";s:17:"Active Ingredient";s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:50:{i:0;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1734;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Retinol palmitat 2500 I.U";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1734;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Retinol palmitat 2500 I.U";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:1;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1766;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Zinc Sulfate";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1766;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Zinc Sulfate";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:2;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1765;s:12:"attribute_id";i:44;s:10:"admin_name";s:18:"Cetrimonium bromid";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1765;s:12:"attribute_id";i:44;s:10:"admin_name";s:18:"Cetrimonium bromid";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:3;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1764;s:12:"attribute_id";i:44;s:10:"admin_name";s:18:"Paracetamol 325 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1764;s:12:"attribute_id";i:44;s:10:"admin_name";s:18:"Paracetamol 325 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:4;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1763;s:12:"attribute_id";i:44;s:10:"admin_name";s:20:"Acetylcysteine 100mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1763;s:12:"attribute_id";i:44;s:10:"admin_name";s:20:"Acetylcysteine 100mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:5;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1762;s:12:"attribute_id";i:44;s:10:"admin_name";s:10:"Vitamin PP";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1762;s:12:"attribute_id";i:44;s:10:"admin_name";s:10:"Vitamin PP";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:6;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1761;s:12:"attribute_id";i:44;s:10:"admin_name";s:10:"Folic Acid";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1761;s:12:"attribute_id";i:44;s:10:"admin_name";s:10:"Folic Acid";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:7;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1760;s:12:"attribute_id";i:44;s:10:"admin_name";s:6:"Biotin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1760;s:12:"attribute_id";i:44;s:10:"admin_name";s:6:"Biotin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:8;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1759;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Dl-alphatocopheryl Acetat";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1759;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Dl-alphatocopheryl Acetat";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:9;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1758;s:12:"attribute_id";i:44;s:10:"admin_name";s:9:"Trà xanh";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1758;s:12:"attribute_id";i:44;s:10:"admin_name";s:9:"Trà xanh";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:10;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1757;s:12:"attribute_id";i:44;s:10:"admin_name";s:5:"Tỏi";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1757;s:12:"attribute_id";i:44;s:10:"admin_name";s:5:"Tỏi";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:11;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1756;s:12:"attribute_id";i:44;s:10:"admin_name";s:16:"Promethazin 90mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1756;s:12:"attribute_id";i:44;s:10:"admin_name";s:16:"Promethazin 90mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:12;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1755;s:12:"attribute_id";i:44;s:10:"admin_name";s:18:"Paracetamol 250mg.";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1755;s:12:"attribute_id";i:44;s:10:"admin_name";s:18:"Paracetamol 250mg.";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:13;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1754;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Ketoconazol 0";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1754;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Ketoconazol 0";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:14;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1753;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Acyclovir 5%";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1753;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Acyclovir 5%";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:15;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1752;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Natribenzoat";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1752;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Natribenzoat";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:16;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1751;s:12:"attribute_id";i:44;s:10:"admin_name";s:8:"Aspartam";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1751;s:12:"attribute_id";i:44;s:10:"admin_name";s:8:"Aspartam";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:17;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1750;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Nghệ vàng";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1750;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Nghệ vàng";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:18;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1749;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Tử tô tử";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1749;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Tử tô tử";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:19;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1748;s:12:"attribute_id";i:44;s:10:"admin_name";s:11:"MEBENDAZOLE";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1748;s:12:"attribute_id";i:44;s:10:"admin_name";s:11:"MEBENDAZOLE";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:20;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1747;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Acid benzoic";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1747;s:12:"attribute_id";i:44;s:10:"admin_name";s:12:"Acid benzoic";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:21;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1746;s:12:"attribute_id";i:44;s:10:"admin_name";s:20:"ASCORBIC ACID 1g/5ml";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1746;s:12:"attribute_id";i:44;s:10:"admin_name";s:20:"ASCORBIC ACID 1g/5ml";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:22;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1745;s:12:"attribute_id";i:44;s:10:"admin_name";s:61:"Cao đặc qui về khan(tương ứng Hà thủ ô đỏ 2g)";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1745;s:12:"attribute_id";i:44;s:10:"admin_name";s:61:"Cao đặc qui về khan(tương ứng Hà thủ ô đỏ 2g)";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:23;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1744;s:12:"attribute_id";i:44;s:10:"admin_name";s:17:"Calcium Carbonate";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1744;s:12:"attribute_id";i:44;s:10:"admin_name";s:17:"Calcium Carbonate";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:24;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1743;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Calcium lactate gluconate";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1743;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Calcium lactate gluconate";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:25;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1742;s:12:"attribute_id";i:44;s:10:"admin_name";s:14:"Cao Tam Thất";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1742;s:12:"attribute_id";i:44;s:10:"admin_name";s:14:"Cao Tam Thất";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:26;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1741;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Cao Đan Sâm";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1741;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Cao Đan Sâm";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:27;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1740;s:12:"attribute_id";i:44;s:10:"admin_name";s:16:"Natri hyaluronat";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1740;s:12:"attribute_id";i:44;s:10:"admin_name";s:16:"Natri hyaluronat";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:28;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1739;s:12:"attribute_id";i:44;s:10:"admin_name";s:9:"Acid Amin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1739;s:12:"attribute_id";i:44;s:10:"admin_name";s:9:"Acid Amin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:29;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1738;s:12:"attribute_id";i:44;s:10:"admin_name";s:15:"Benzoyl Peroxid";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1738;s:12:"attribute_id";i:44;s:10:"admin_name";s:15:"Benzoyl Peroxid";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:30;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1737;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Thiamin hydroclorid 20mg.";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1737;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Thiamin hydroclorid 20mg.";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:31;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1736;s:12:"attribute_id";i:44;s:10:"admin_name";s:14:"Riboflavin 5mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1736;s:12:"attribute_id";i:44;s:10:"admin_name";s:14:"Riboflavin 5mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:32;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1735;s:12:"attribute_id";i:44;s:10:"admin_name";s:21:"Cholin bitartrat 25mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1735;s:12:"attribute_id";i:44;s:10:"admin_name";s:21:"Cholin bitartrat 25mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:33;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1718;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Dầu Parafin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1718;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Dầu Parafin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:34;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1703;s:12:"attribute_id";i:44;s:10:"admin_name";s:22:"Natri clorid 90mg/10ml";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1703;s:12:"attribute_id";i:44;s:10:"admin_name";s:22:"Natri clorid 90mg/10ml";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:35;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1704;s:12:"attribute_id";i:44;s:10:"admin_name";s:22:"Berberin clorid 100 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1704;s:12:"attribute_id";i:44;s:10:"admin_name";s:22:"Berberin clorid 100 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:36;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1705;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Gel nhôm phosphat 20% 12";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1705;s:12:"attribute_id";i:44;s:10:"admin_name";s:25:"Gel nhôm phosphat 20% 12";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:37;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1706;s:12:"attribute_id";i:44;s:10:"admin_name";s:3:"38g";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1706;s:12:"attribute_id";i:44;s:10:"admin_name";s:3:"38g";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:38;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1707;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Loratadin 5mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1707;s:12:"attribute_id";i:44;s:10:"admin_name";s:13:"Loratadin 5mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:39;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1708;s:12:"attribute_id";i:44;s:10:"admin_name";s:16:"Mebendazol 500mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1708;s:12:"attribute_id";i:44;s:10:"admin_name";s:16:"Mebendazol 500mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:40;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1709;s:12:"attribute_id";i:44;s:10:"admin_name";s:17:"Paracetamol 500mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1709;s:12:"attribute_id";i:44;s:10:"admin_name";s:17:"Paracetamol 500mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:41;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1710;s:12:"attribute_id";i:44;s:10:"admin_name";s:20:"Clopheramine HCl 2mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1710;s:12:"attribute_id";i:44;s:10:"admin_name";s:20:"Clopheramine HCl 2mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:42;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1711;s:12:"attribute_id";i:44;s:10:"admin_name";s:23:"Phenylepherine HCl 10mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1711;s:12:"attribute_id";i:44;s:10:"admin_name";s:23:"Phenylepherine HCl 10mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:43;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1712;s:12:"attribute_id";i:44;s:10:"admin_name";s:44:"Paracetamol 250 mgClorpheniramin maleat 2 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1712;s:12:"attribute_id";i:44;s:10:"admin_name";s:44:"Paracetamol 250 mgClorpheniramin maleat 2 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:44;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1713;s:12:"attribute_id";i:44;s:10:"admin_name";s:54:"Cao Carduus marianus (tương đương 140mg Silymarin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1713;s:12:"attribute_id";i:44;s:10:"admin_name";s:54:"Cao Carduus marianus (tương đương 140mg Silymarin";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:45;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1714;s:12:"attribute_id";i:44;s:10:"admin_name";s:19:"Silybin 60mg) 200mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1714;s:12:"attribute_id";i:44;s:10:"admin_name";s:19:"Silybin 60mg) 200mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:46;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1715;s:12:"attribute_id";i:44;s:10:"admin_name";s:10:"Loperamide";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1715;s:12:"attribute_id";i:44;s:10:"admin_name";s:10:"Loperamide";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:47;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1716;s:12:"attribute_id";i:44;s:10:"admin_name";s:17:"Mebendazole 500mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1716;s:12:"attribute_id";i:44;s:10:"admin_name";s:17:"Mebendazole 500mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:48;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1717;s:12:"attribute_id";i:44;s:10:"admin_name";s:15:"Dibencozid 3 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1717;s:12:"attribute_id";i:44;s:10:"admin_name";s:15:"Dibencozid 3 mg";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:49;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1728;s:12:"attribute_id";i:44;s:10:"admin_name";s:22:"Isopropyl Methylphenol";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:1728;s:12:"attribute_id";i:44;s:10:"admin_name";s:22:"Isopropyl Methylphenol";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}}s:28:" * escapeWhenCastingToString";b:0;}s:9:"lazy_load";b:1;}i:74;a:5:{s:3:"key";s:35:"product|children::active_ingredient";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";r:8212;s:9:"lazy_load";b:1;}i:75;a:5:{s:3:"key";s:33:"product|parent::active_ingredient";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";r:8212;s:9:"lazy_load";b:1;}i:76;a:5:{s:3:"key";s:17:"product|unit_type";s:4:"type";s:6:"select";s:5:"label";s:9:"Unit Type";s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:13:{i:0;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:22;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Vỉ";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:22;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Vỉ";s:10:"sort_order";i:1;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:1;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:23;s:12:"attribute_id";i:45;s:10:"admin_name";s:5:"Hộp";s:10:"sort_order";i:2;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:23;s:12:"attribute_id";i:45;s:10:"admin_name";s:5:"Hộp";s:10:"sort_order";i:2;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:2;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:104;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Gói";s:10:"sort_order";i:3;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:104;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Gói";s:10:"sort_order";i:3;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:3;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:105;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Chai";s:10:"sort_order";i:4;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:105;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Chai";s:10:"sort_order";i:4;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:4;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:106;s:12:"attribute_id";i:45;s:10:"admin_name";s:5:"Tuýp";s:10:"sort_order";i:5;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:106;s:12:"attribute_id";i:45;s:10:"admin_name";s:5:"Tuýp";s:10:"sort_order";i:5;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:5;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:107;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Lọ";s:10:"sort_order";i:6;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:107;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Lọ";s:10:"sort_order";i:6;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:6;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:108;s:12:"attribute_id";i:45;s:10:"admin_name";s:5:"Viên";s:10:"sort_order";i:7;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:108;s:12:"attribute_id";i:45;s:10:"admin_name";s:5:"Viên";s:10:"sort_order";i:7;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:7;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:109;s:12:"attribute_id";i:45;s:10:"admin_name";s:11:"Hộp lớn";s:10:"sort_order";i:8;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:109;s:12:"attribute_id";i:45;s:10:"admin_name";s:11:"Hộp lớn";s:10:"sort_order";i:8;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:8;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:110;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Túi";s:10:"sort_order";i:9;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:110;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Túi";s:10:"sort_order";i:9;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:9;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:111;s:12:"attribute_id";i:45;s:10:"admin_name";s:3:"Kit";s:10:"sort_order";i:10;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:111;s:12:"attribute_id";i:45;s:10:"admin_name";s:3:"Kit";s:10:"sort_order";i:10;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:10;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:112;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Bộ";s:10:"sort_order";i:11;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:112;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Bộ";s:10:"sort_order";i:11;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:11;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2118;s:12:"attribute_id";i:45;s:10:"admin_name";s:6:"Bịch";s:10:"sort_order";i:12;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:2118;s:12:"attribute_id";i:45;s:10:"admin_name";s:6:"Bịch";s:10:"sort_order";i:12;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}i:12;O:39:"Webkul\Attribute\Models\AttributeOption":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"attribute_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2119;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Cái";s:10:"sort_order";i:13;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:11:" * original";a:8:{s:2:"id";i:2119;s:12:"attribute_id";i:45;s:10:"admin_name";s:4:"Cái";s:10:"sort_order";i:13;s:12:"swatch_value";N;s:11:"is_featured";i:0;s:11:"brand_image";N;s:17:"brand_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"swatch_value_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:10:"admin_name";i:1;s:12:"swatch_value";i:2;s:10:"sort_order";i:3;s:12:"attribute_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * defaultLocale";N;s:20:"translatedAttributes";a:1:{i:0;s:5:"label";}}}s:28:" * escapeWhenCastingToString";b:0;}s:9:"lazy_load";b:0;}i:77;a:5:{s:3:"key";s:27:"product|children::unit_type";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";r:11032;s:9:"lazy_load";b:0;}i:78;a:5:{s:3:"key";s:25:"product|parent::unit_type";s:4:"type";s:6:"select";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";r:11032;s:9:"lazy_load";b:0;}i:79;a:5:{s:3:"key";s:21:"product|visible_price";s:4:"type";s:7:"boolean";s:5:"label";s:13:"Visible Price";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:80;a:5:{s:3:"key";s:31:"product|children::visible_price";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:81;a:5:{s:3:"key";s:29:"product|parent::visible_price";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:82;a:5:{s:3:"key";s:17:"product|exclusive";s:4:"type";s:7:"boolean";s:5:"label";s:9:"Exclusive";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:83;a:5:{s:3:"key";s:27:"product|children::exclusive";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:84;a:5:{s:3:"key";s:25:"product|parent::exclusive";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:85;a:5:{s:3:"key";s:20:"product|manufacturer";s:4:"type";s:4:"text";s:5:"label";s:17:"Nhà sản xuất";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:86;a:5:{s:3:"key";s:30:"product|children::manufacturer";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:87;a:5:{s:3:"key";s:28:"product|parent::manufacturer";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:88;a:5:{s:3:"key";s:27:"product|registration_number";s:4:"type";s:4:"text";s:5:"label";s:15:"Số đăng ký";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:89;a:5:{s:3:"key";s:37:"product|children::registration_number";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:90;a:5:{s:3:"key";s:35:"product|parent::registration_number";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:91;a:5:{s:3:"key";s:21:"product|specification";s:4:"type";s:4:"text";s:5:"label";s:9:"Quy cách";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:92;a:5:{s:3:"key";s:31:"product|children::specification";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:93;a:5:{s:3:"key";s:29:"product|parent::specification";s:4:"type";s:4:"text";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:94;a:5:{s:3:"key";s:23:"product|company_program";s:4:"type";s:7:"boolean";s:5:"label";s:15:"Company Program";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:95;a:5:{s:3:"key";s:33:"product|children::company_program";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ con";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}i:96;a:5:{s:3:"key";s:31:"product|parent::company_program";s:4:"type";s:7:"boolean";s:5:"label";s:28:"Tên thuộc tính chỉ cha";s:7:"options";a:0:{}s:9:"lazy_load";b:0;}}}}