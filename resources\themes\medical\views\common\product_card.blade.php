{{-- Common Product Card Component --}}
@php
    // L<PERSON>y các tham số từ include
    $productId = $product->id ?? null;
    $productName = $product->name ?? '';
    $productImage = $product->images->first() ? asset('storage/' . $product->images->first()->path) : asset('images/product.png');
    $productDiscount = $product->discount_percent ??  $product->discount ?? 0;
    $productPrice = $product->discounted_price ?? $product->price ?? 0;
    $productOriginalPrice = $product->original_price ?? 0;
    $productVisiblePrice = $product->visible_price ?? false;
    $productHasDiscount = ($product->has_discount || $product->discount_percent) ?? false;
    $productUnitTypeName = $product->unit_type_name ?? 'sản phẩm';

    // Kiểm tra user đăng nhập
    $isLoggedIn = Auth::guard('customer')->check();

    // Logic hiển thị
    $showDiscount = $productDiscount && $productVisiblePrice && $isLoggedIn;
    $showPrice = $productPrice && $productVisiblePrice;
    $showOriginalPrice = $productHasDiscount && $isLoggedIn && $showPrice;
    $showCurrentPrice = $isLoggedIn && $showPrice;

    // Button text logic
    if ($isLoggedIn) {
        $buttonText = ($productPrice && $productVisiblePrice) ? 'Thêm vào giỏ hàng' : 'Báo giá';
    } else {
        $buttonText = ($productPrice && $productVisiblePrice) ? 'Đăng nhập để xem giá' : 'Đăng nhập để báo giá';
    }
@endphp

<style>
.product-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 360px; /* Giảm chiều cao để gọn gàng hơn */
}

.product-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    height: calc(1.4em * 2);
}
</style>

<div class="bg-white rounded-2xl border border-gray-300 p-3 product-card">
    <a href="{{ route('product_detail', ['productId' => $productId]) }}" class="product-link flex flex-col h-full">
        <div class="relative product-image-container">
            <img src="{{ $productImage }}" alt="Product" class="w-full h-48 object-cover rounded-lg">
            @if($showDiscount)
                <span class="discount absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-sm rounded">-{{ $productDiscount }}%</span>
            @endif
        </div>
        <div class="mt-3 product-content">
            <h3 class="font-semibold text-gray-800 mb-2 product-title">{{ $productName }}</h3>
            <div class="product-price-section">
                @if($showPrice)
                    @if($isLoggedIn)
                        @if($showOriginalPrice)
                            <p class="text-gray-500 line-through text-sm">{{ number_format($productOriginalPrice) }}đ</p>
                            <p class="text-[#F06F22] font-semibold">{{ number_format($productPrice) }}đ/ {{ $productUnitTypeName }}</p>
                        @else
                            <div class="text-sm">&nbsp;</div>
                            <p class="text-[#F06F22] font-semibold">{{ number_format($productPrice) }}đ/ {{ $productUnitTypeName }}</p>
                        @endif
                    @else
                        <div class="h-[40px]"></div>
                    @endif
                @else
                    <div class="h-[40px]"></div>
                @endif
            </div>
        </div>
    </a>
    <button class="mt-3 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors w-full quick-order-add-btn product-button" data-product-id="{{ $productId }}">
        {{ $buttonText }}
    </button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Xử lý click button cho product card
    const productButtons = document.querySelectorAll('.quick-order-add-btn');

    productButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const productId = this.getAttribute('data-product-id');
            const isLoggedIn = {{ $isLoggedIn ? 'true' : 'false' }};

            if (!isLoggedIn) {
                // Hiển thị thông báo và redirect đến trang đăng nhập
                setTimeout(() => {
                    window.location.href = '{{ route("signin") }}';
                }, 1500);
                return;
            }

            // Logic thêm vào giỏ hàng hoặc báo giá
            const hasVisiblePrice = {{ $productVisiblePrice ? 'true' : 'false' }};

            if (hasVisiblePrice) {
                // Thêm vào giỏ hàng
                console.log('Adding to cart:', productId);
                // TODO: Implement add to cart API call
            } else {
                // Báo giá
                console.log('Request quote for:', productId);
                // TODO: Implement quote request API call
            }
        });
    });
});
</script>
