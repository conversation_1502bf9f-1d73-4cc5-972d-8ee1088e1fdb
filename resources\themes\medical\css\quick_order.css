.quick-order-container {
    max-width: 1200px;
    margin: 32px auto;
    padding: 0 16px;
}
.quick-order-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 16px;
}
.quick-order-search-section {
    margin-bottom: 18px;
}
.quick-order-search-label {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 6px;
    display: block;
}
.quick-order-search-bar {
    display: flex;
    gap: 8px;
    margin-top: 6px;
}
.quick-order-search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}
.quick-order-search-btn {
    background: #FF6B00;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 18px;
    font-size: 1rem;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.15s;
}
.quick-order-search-btn:hover {
    background: #e65c00;
}
.quick-order-content {
    display: flex;
    gap: 24px;
}
.quick-order-sidebar {
    width: 240px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 18px;
}
.quick-order-filter {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px 14px;
}
.quick-order-filter-title {
    font-weight: 600;
    font-size: 1.08rem;
    margin-bottom: 10px;
}
.quick-order-filter-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 120px;
    overflow-y: auto;
    padding-right: 4px;
}
.scrollable-list::-webkit-scrollbar {
    width: 6px;
    background: #f5f5f5;
}
.scrollable-list::-webkit-scrollbar-thumb {
    background: #e0e0e0;
    border-radius: 4px;
}
.quick-order-products {
    display: flex;
    gap: 18px;
    flex-wrap: wrap;
    flex: 1;
}
.quick-order-product-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    width: 220px;
    padding: 16px 12px 18px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 18px;
    box-shadow: 0 2px 8px #0001;
}
.quick-order-product-img {
    width: 100%;
    height: 140px;
    background: #f5f5f5;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    position: relative;
}
.discount-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #f44336;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}
.quick-order-product-img img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
.quick-order-product-name {
    font-weight: 600;
    font-size: 1.08rem;
    margin-bottom: 4px;
    text-align: center;
    line-height: 1.2;
    height: 2.4em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.quick-order-product-brand,
.quick-order-product-unit {
    font-size: 0.97rem;
    color: #444;
    margin-bottom: 2px;
    text-align: center;
}
.quick-order-product-price {
    margin: 8px 0 12px 0;
    text-align: center;
}
.price-new {
    color: #FF6B00;
    font-weight: 600;
    font-size: 1.08rem;
    margin-right: 8px;
}
.price-old {
    color: #888;
    text-decoration: line-through;
    font-size: 0.98rem;
}
.quick-order-buy-btn {
    background: #FF6B00;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 18px;
    font-size: 1rem;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.15s;
}
.quick-order-buy-btn:hover {
    background: #e65c00;
}

/* pagination */
.quick-order-pagination {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin: 24px 0 0 0;
}
.quick-order-page-btn {
    background: #f5f7fa;
    border: 1px solid #eee;
    border-radius: 6px;
    padding: 6px 16px;
    cursor: pointer;
    color: #333;
    font-size: 1rem;
    transition: background 0.15s, color 0.15s;
}
.quick-order-page-btn.active,
.quick-order-page-btn:hover {
    background: #FF6B00;
    color: #fff;
    border-color: #FF6B00;
}

@media (max-width: 900px) {
    .quick-order-content {
        flex-direction: column;
    }
    .quick-order-sidebar {
        width: 100%;
        flex-direction: row;
        gap: 18px;
    }
    .quick-order-products {
        justify-content: center;
    }
}

.quick-order-qty-wrapper {
    display: flex;
    align-items: center;
    gap: 0;
    margin-bottom: 8px;
}

/* add/subtract button */
.quick-order-qty-wrapper {
    display: flex;
    align-items: center;
    gap: 0;
    margin-bottom: 8px;
}
.quick-order-qty-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f3f4f6;
    color: #f26522;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Loại bỏ margin/padding mặc định nếu có */
    margin: 0;
    padding: 0;
    margin-bottom: 8px;
}
.quick-order-qty-btn:hover {
    background: #f26522;
    color: #fff;
}
.quick-order-qty-input {
    width: 48px;
    text-align: center;
    font-size: 1rem;
    border: 1px solid #ccc;      /* Ensure all borders are present */
    border-radius: 0;
    margin: 0 2px;
    padding: 0 4px;
    height: 32px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 10px;
}

/* Ẩn spinner trên Chrome, Safari, Edge */
.quick-order-qty-input::-webkit-outer-spin-button,
.quick-order-qty-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Ẩn spinner trên Firefox */
.quick-order-qty-input[type=number] {
    -moz-appearance: textfield;
}