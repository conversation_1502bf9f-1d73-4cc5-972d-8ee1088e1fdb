<?php

namespace App\Http\Controllers\Medical;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Webkul\Product\Models\Product;
use App\Http\Controllers\Medical\PaginationController;

class QuickOrderController extends Controller
{
    protected $productController;

    public function __construct(ProductController $productController)
    {
        $this->productController = $productController;
    }

    public function index(Request $request) 
    {
        $query = Product::query();

        // Nếu có search thì mới thêm điều kiện tìm kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('attribute_values', function($q) use ($search) {
                $q->whereHas('attribute', function($attr) {
                    $attr->where('code', 'name');
                })->where('text_value', 'like', "%{$search}%");
            });
        }

        // Chỉ lấy sản phẩm hiển thị giá
        $query->whereHas('attribute_values', function($query) {
            $query->whereHas('attribute', function($q) {
                $q->where('code', 'visible_price');
            })->where('boolean_value', 1);
        });

        $products = $query->orderBy('id', 'desc')->get();

        // Xử lý dữ liệu sản phẩm
        $products = $this->productController->processProductData($products);
        

        $paginated = PaginationController::paginateCollection($products, 6);
        return view('medical::quick_order.quick_order', ['products' => $paginated]);

    }
}
