@extends('medical::layouts.app')

@section('content')
@vite(['resources/themes/medical/css/product_detail.css', 'resources/themes/medical/css/input-focus.css'])

<div id="product" class="product-detail-container" data-product-id="{{ $product->id }}">
    <div class="product-gallery">
        <div class="main-image">
            @php
            $mainImage = $product->images->first() ? asset('storage/' . $product->images->first()->path) : asset('images/product.png');
            @endphp
            <img id="main-product-image" src="{{ $mainImage }}" alt="{{ $product->name }}">
        </div>
        <div class="thumbnail-container">
            <button id="prev-btn" class="nav-btn prev-btn">&lt;</button>
            <div class="thumbnails-wrapper">
                <div class="thumbnails">
                    @foreach($product->images as $image)
                    <div class="thumbnail {{ $loop->first ? 'active' : '' }}">
                        <img src="{{ asset('storage/' . $image->path) }}" alt="{{ $product->name }} {{ $loop->iteration }}" onclick="changeImage(this)">
                    </div>
                    @endforeach
                </div>
            </div>
            <button id="next-btn" class="nav-btn next-btn">&gt;</button>
        </div>
    </div>

    <div class="product-info">
        <h1 class="product-title">{{ $product->name }}</h1>
        <div class="product-code">
            {{ $product->sku }}
            @if(isset($productAttributes['brand']))
                • Thương hiệu: <a href="#" class="brand-link">{{ $productAttributes['brand']['value'] }}</a>
            @endif
        </div>

        <div class="product-registration">
            @if(isset($productAttributes['registration_number']))
                <div class="reg-number">Số đăng ký: {{ $productAttributes['registration_number']['value'] }}</div>
            @endif
        </div>

        @if(isset($productAttributes['unit_type']))
        <div class="product-category">
            <div class="category-label">{{ $productAttributes['unit_type']['label'] }}</div>
            <div class="category-options">
                <button class="category-btn active" data-unit-type="{{ $productAttributes['unit_type']['option_id'] }}">{{ $productAttributes['unit_type']['value'] }}</button>
            </div>
        </div>
        @endif

        <div class="product-warning {{ (!Auth::guard('customer')->check() || !$product->visible_price) ? 'product-warning-bg' : '' }}">
            @if(Auth::guard('customer')->check())
                <div class="warning-text">
                    @if($product->visible_price)
                        @if($product->has_discount || $product->discount_percent)
                            <span class="text-gray-500 line-through mr-2">{{ number_format($product->original_price) }}đ</span>
                            {{ number_format($product->discounted_price ?? $product->price) }}đ
                        @else
                            {{ number_format($product->price) }}đ
                        @endif
                    @else
                        Liên hệ
                    @endif
                </div>
            @else
                <div class="warning-text">Đăng nhập để xem giá</div>
            @endif
        </div>

        <div class="product-specs">
            <div class="spec-item">
                <div class="spec-label">Danh mục</div>
                <div class="spec-value"><div>{{ $product->categories->last() ? $product->categories->last()->translation->name : 'Chưa phân loại' }}</div></div>
            </div>
            @if(isset($productAttributes['active_ingredient']))
            <div class="spec-item">
                <div class="spec-label">{{ $productAttributes['active_ingredient']['label'] }}</div>
                <div class="spec-value">{{ $productAttributes['active_ingredient']['value'] }}</div>
            </div>
            @endif
            @if(isset($productAttributes['specification']))
            <div class="spec-item">
                <div class="spec-label">{{ $productAttributes['specification']['label'] }}</div>
                <div class="spec-value">{{ $productAttributes['specification']['value'] }}</div>
            </div>
            @endif
            @if(isset($productAttributes['dosage_form']))
            <div class="spec-item">
                <div class="spec-label">{{ $productAttributes['dosage_form']['label'] }}</div>
                <div class="spec-value">{{ $productAttributes['dosage_form']['value'] }}</div>
            </div>
            @endif
            @if(isset($productAttributes['indication']))
            <div class="spec-item">
                <div class="spec-label">{{ $productAttributes['indication']['label'] }}</div>
                <div class="spec-value">{{ $productAttributes['indication']['value'] }}</div>
            </div>
            @endif
            @if(isset($productAttributes['manufacturer']))
            <div class="spec-item">
                <div class="spec-label">{{ $productAttributes['manufacturer']['label'] }}</div>
                <div class="spec-value">{{ $productAttributes['manufacturer']['value'] }}</div>
            </div>
            @endif
            <div class="spec-item">
                <div class="spec-label">Lưu ý</div>
                <div class="spec-value">Sản phẩm này chỉ bán khi có chỉ định của bác sĩ, mọi thông tin trên đây chỉ mang tính chất tham khảo. Vui lòng đọc kĩ thông tin chi tiết ở tờ hướng dẫn sử dụng của sản phẩm.</div>
            </div>
        </div>
    </div>
    <!-- quantity box -->
    <div class="fixed-quantity-panel">
        <div class="quantity-label">Số lượng</div>
        <div class="quantity-controls">
            <button class="quantity-btn minus" onclick="decreaseQuantity()">−</button>
            <input type="text" id="quantity" value="1" readonly>
            <button class="quantity-btn plus" onclick="increaseQuantity()">+</button>
        </div>
        @if(Auth::guard('customer')->check())
            @if($product->visible_price)
                <div class="button-group">
                    <button class="buy-now-btn" onclick="buyNow()">Mua ngay</button>
                    <button class="quote-btn" onclick="addToQuote()">Báo giá</button>
                </div>
            @else
                <button class="quote-btn full-width" onclick="addToQuote()">Báo giá</button>
            @endif
        @else
            <button class="consult-btn" onclick="window.location.href='/signin'">
                Đăng nhập để xem giá
            </button>
        @endif
    </div>
</div>

<!-- product description -->
<div class="product-description-container">
    <div class="description-tabs">
        <button class="tab-btn active" data-target="mo-ta">Mô tả</button>
        <button class="tab-btn" data-target="thanh-phan">Thành phần</button>
        <button class="tab-btn" data-target="huong-dan-su-dung">Hướng dẫn sử dụng</button>
        <button class="tab-btn" data-target="chi-dinh">Chỉ định</button>
        <button class="tab-btn" data-target="than-trong">Thận trọng</button>
        <button class="tab-btn" data-target="thong-tin-san-xuat">Thông tin sản xuất</button>
    </div>

    <div class="product-description collapsed">
        <div class="description-content">
            @if(isset($productAttributes['description']))
                <h2 id="mo-ta">{{ $product->name }} là gì?</h2>
                <div>{!! $productAttributes['description']['value'] !!}</div>
            @endif

            @if(isset($productAttributes['ingredients']))
                <h2 id="thanh-phan">Thành phần của {{ $product->name }}</h2>
                <div>{!! $productAttributes['ingredients']['value'] !!}</div>
            @endif

            @if(isset($productAttributes['usage_instructions']))
                <h2 id="huong-dan-su-dung">Cách dùng {{ $product->name }}</h2>
                <div>{!! $productAttributes['usage_instructions']['value'] !!}</div>
            @endif

            @if(isset($productAttributes['indication']))
                <h2 id="chi-dinh">Chỉ định của {{ $product->name }}</h2>
                <div>{!! $productAttributes['indication']['value'] !!}</div>
            @endif

            @if(isset($productAttributes['precautions']))
                <h2 id="than-trong">Thận trọng khi dùng {{ $product->name }}</h2>
                <div>{!! $productAttributes['precautions']['value'] !!}</div>
            @endif

            @if(isset($productAttributes['manufacturing_info']))
                <h2 id="thong-tin-san-xuat">Thông tin sản xuất</h2>
                <div>{!! $productAttributes['manufacturing_info']['value'] !!}</div>
            @endif

            @if(!isset($productAttributes['description']) && !isset($productAttributes['ingredients']) && !isset($productAttributes['usage_instructions']) && !isset($productAttributes['indication']) && !isset($productAttributes['precautions']) && !isset($productAttributes['manufacturing_info']))
                <h2 id="mo-ta">{{ $product->name }}</h2>
                <p>Thông tin chi tiết về sản phẩm đang được cập nhật. Vui lòng liên hệ với chúng tôi để biết thêm thông tin.</p>
            @endif
        </div>
        <div class="description-fade"></div>
        <button class="toggle-description">Xem thêm</button>
    </div>
</div>

<!-- comment section -->
<div class="qa-section-container">
    <hr class="border-t border-gray-300 my-4">
    <div class="qa-section">
        <div class="qa-header">
            <h2 class="qa-title">Hỏi & Đáp <span class="qa-count">({{ count($productQuestions) }})</span></h2>
            @if(Auth::guard('customer')->check())
                <button class="ask-question-btn" onclick="showQuestionForm()">Gửi câu hỏi</button>
            @else
                <button class="ask-question-btn" onclick="window.location.href='/signin'">Đăng nhập để gửi câu hỏi</button>
            @endif
        </div>

        <!-- Question Form -->
        @if(Auth::guard('customer')->check())
        <div id="question-form" class="question-form" style="display: none;">
            <div class="form-group">
                <textarea id="question-text" placeholder="Nhập câu hỏi của bạn..." rows="3" class="w-full p-3 border border-gray-300 rounded-lg resize-none"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" onclick="hideQuestionForm()" class="cancel-btn">Hủy</button>
                <button type="button" onclick="submitQuestion()" class="submit-btn">Gửi câu hỏi</button>
            </div>
        </div>
        @endif

        <div class="qa-filters">
            <button class="filter-btn active" data-filter="newest">Mới nhất</button>
            <button class="filter-btn" data-filter="oldest">Cũ nhất</button>
        </div>

        <div class="qa-content" id="qa-content">
            @forelse($productQuestions as $question)
            <div class="qa-item" data-created="{{ $question->created_at->timestamp }}">
                <div class="user-question">
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="/images/avatar.png" alt="User">
                        </div>
                        <div class="user-details">
                            <div class="user-name-badge">
                                <span class="user-name">{{ $question->customer ? $question->customer->name : $question->guest_name }}</span>
                            </div>
                            <span class="question-time">{{ $question->created_at->format('H:i d-m-Y') }}</span>
                        </div>
                    </div>
                    <p class="question-text">{{ $question->question }}</p>
                </div>

                @if($question->answer)
                <div class="admin-answer">
                    <div class="admin-info">
                        <div class="admin-avatar">
                            <img src="/images/logo_full.png" alt="Dược Phan Anh">
                        </div>
                        <div class="admin-details">
                            <div class="admin-name-badge">
                                <span class="admin-name">Dược Phan Anh</span>
                                <span class="verified-badge"></span>
                            </div>
                            <span class="answer-time">{{ $question->answer->created_at->format('H:i d-m-Y') }}</span>
                        </div>
                    </div>
                    <div class="answer-text">
                        {!! nl2br(e($question->answer->answer)) !!}
                    </div>
                </div>
                @endif
            </div>
            @empty
            <div class="no-questions">
                <p>Chưa có câu hỏi nào cho sản phẩm này. Hãy là người đầu tiên đặt câu hỏi!</p>
            </div>
            @endforelse
        </div>
        @include('medical::common.view_more', [
            'parentClass' => 'qa-content',
            'childClass' => 'qa-item',
            'perPage' => 3,
            'btnTextMore' => 'Xem thêm',
            'btnTextLess' => 'Thu nhỏ'
        ])
    </div>
</div>
</div>

<!-- products -->
@if($relatedProducts && $relatedProducts->count() > 0)
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Sản phẩm cùng thương hiệu</h2>
        @if(isset($productAttributes['brand']) && isset($productAttributes['brand']['option_id']))
        <button class="more-button mt-4 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors"
                onclick="window.location.href='/brands_detail?brand_id={{ $productAttributes['brand']['option_id'] }}'">
            Xem thêm >
        </button>
        @endif
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        @foreach($relatedProducts as $relatedProduct)
        <div class="bg-white rounded-lg border border-gray-300 p-4 flex flex-col h-full product-card"
             onclick="window.location.href='{{ route('product_detail', ['productId' => $relatedProduct->id]) }}'"
             style="cursor: pointer;">
            <div class="relative">
                <img src="{{ $relatedProduct->images->first() ? asset('storage/' . $relatedProduct->images->first()->path) : asset('images/product.png') }}"
                     alt="{{ $relatedProduct->name }}" class="w-full h-48 object-cover rounded-lg">
                @if(Auth::guard('customer')->check() && $relatedProduct->has_discount && $relatedProduct->visible_price)
                <span class="discount absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-sm rounded">-{{ $relatedProduct->discount }}%</span>
                @endif
            </div>
            <div class="mt-4 flex-grow flex flex-col">
                <h3 class="font-semibold text-gray-800 mb-2 line-clamp-2">{{ $relatedProduct->name }}</h3>
                <div class="mt-auto">
                    @if($relatedProduct->price && $relatedProduct->visible_price)
                        @if(Auth::guard('customer')->check())
                            @if($relatedProduct->has_discount)
                                <p class="text-gray-500 line-through text-sm">{{ number_format($relatedProduct->original_price) }}đ</p>
                                <p class="text-[#F06F22] font-semibold">{{ number_format($relatedProduct->price) }}đ/1 {{ $relatedProduct->unit ?? 'sản phẩm' }}</p>
                            @else
                                <p class="text-[#F06F22] font-semibold">{{ number_format($relatedProduct->price) }}đ/1 {{ $relatedProduct->unit ?? 'sản phẩm' }}</p>
                            @endif
                        @else
                            <div class="h-[40px]"></div>
                        @endif
                    @else
                        <div class="h-[40px]"></div>
                    @endif
                </div>
            </div>
            <button class="mt-4 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors w-full quick-order-add-btn"
                    data-product-id="{{ $relatedProduct->id }}"
                    onclick="event.stopPropagation();">
                @if(Auth::guard('customer')->check())
                    @if($relatedProduct->price && $relatedProduct->visible_price)
                        Thêm vào giỏ hàng
                    @else
                        Báo giá
                    @endif
                @else
                    @if($relatedProduct->price && $relatedProduct->visible_price)
                        Đăng nhập để xem giá
                    @else
                        Đăng nhập để báo giá
                    @endif
                @endif
            </button>
        </div>
        @endforeach
    </div>
</div>
@endif



<script>
    // Xử lý chức năng thay đổi ảnh khi click vào thumbnail
    function changeImage(element) {
        // Cập nhật ảnh chính
        const mainImage = document.getElementById('main-product-image');
        mainImage.src = element.src;
        mainImage.alt = element.alt;

        // Cập nhật trạng thái active cho thumbnail
        const thumbnails = document.querySelectorAll('.thumbnail');
        thumbnails.forEach(thumbnail => {
            thumbnail.classList.remove('active');
        });

        // Thêm class active cho thumbnail được click
        element.parentElement.classList.add('active');
    }

    // Xử lý chức năng tăng giảm số lượng
    function increaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        let quantity = parseInt(quantityInput.value);
        quantityInput.value = quantity + 1;
    }

    function decreaseQuantity() {
        const quantityInput = document.getElementById('quantity');
        let quantity = parseInt(quantityInput.value);
        if (quantity > 1) {
            quantityInput.value = quantity - 1;
        }
    }

    // Xử lý thêm vào giỏ hàng
    function addToCart() {
        // Kiểm tra xem người dùng đã đăng nhập chưa
        const isLoggedIn = document.body.classList.contains('customer-logged-in');

        if (!isLoggedIn) {
            // Nếu chưa đăng nhập, chuyển hướng đến trang đăng nhập
            window.location.href = '/signin';
            return;
        }

        // Nếu đã đăng nhập, thực hiện thêm vào giỏ hàng
        const productId = document.getElementById('product').getAttribute('data-product-id');
        const quantity = document.getElementById('quantity').value;

        // Gọi API thêm vào giỏ hàng
        fetch('/api/cart/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hiển thị thông báo thành công
                Toastify({
                    text: "Đã thêm sản phẩm vào giỏ hàng",
                    duration: 1500,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#4CAF50",
                    close: true
                }).showToast();

                // Cập nhật số lượng sản phẩm trong giỏ hàng (nếu có)
                const cartCountElement = document.querySelector('.cart-count');
                if (cartCountElement) {
                    cartCountElement.textContent = data.cart_count || 0;
                }
            } else {
                // Hiển thị thông báo lỗi
                Toastify({
                    text: data.message || "Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng",
                    duration: 1500,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#F44336",
                    close: true
                }).showToast();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Hiển thị thông báo lỗi
            Toastify({
                text: "Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng",
                duration: 1500,
                gravity: "top",
                position: "right",
                backgroundColor: "#F44336",
                close: true
            }).showToast();
        });
    }

    // Xử lý chức năng scroll thumbnails
    document.addEventListener('DOMContentLoaded', function() {
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const thumbnails = document.querySelector('.thumbnails');

        if (prevBtn && nextBtn && thumbnails) {
            prevBtn.addEventListener('click', function() {
                thumbnails.scrollBy({
                    left: -100,
                    behavior: 'smooth'
                });
            });

            nextBtn.addEventListener('click', function() {
                thumbnails.scrollBy({
                    left: 100,
                    behavior: 'smooth'
                });
            });

            // Kiểm tra xem có cần hiển thị nút điều hướng không
            function checkScrollButtons() {
                const isScrollable = thumbnails.scrollWidth > thumbnails.clientWidth;

                if (!isScrollable) {
                    prevBtn.style.display = 'none';
                    nextBtn.style.display = 'none';
                    return;
                }

                // Kiểm tra vị trí scroll
                updateScrollButtonsVisibility();
            }

            // Cập nhật hiển thị nút dựa trên vị trí scroll
            function updateScrollButtonsVisibility() {
                // Kiểm tra xem đã scroll đến đầu chưa
                const isAtStart = thumbnails.scrollLeft <= 10; // Dùng ngưỡng 10px để tránh lỗi làm tròn

                // Kiểm tra xem đã scroll đến cuối chưa
                const isAtEnd = thumbnails.scrollLeft + thumbnails.clientWidth >= thumbnails.scrollWidth - 10;

                // Cập nhật hiển thị nút
                prevBtn.style.display = isAtStart ? 'none' : 'block';
                nextBtn.style.display = isAtEnd ? 'none' : 'block';
            }

            // Kiểm tra ban đầu
            checkScrollButtons();

            // Kiểm tra khi resize
            window.addEventListener('resize', checkScrollButtons);

            // Kiểm tra khi scroll
            thumbnails.addEventListener('scroll', updateScrollButtonsVisibility);
        }

        // Sử dụng phương pháp đơn giản hơn với position: sticky
        const mediaQuery = window.matchMedia('(min-width: 992px)');
        const gallery = document.querySelector('.product-gallery');
        const quantityPanel = document.querySelector('.fixed-quantity-panel');

        // Chỉ áp dụng sticky khi màn hình lớn hơn 992px
        function handleStickyElements() {
            if (!gallery || !quantityPanel) return;

            if (mediaQuery.matches) {
                // Áp dụng sticky cho màn hình lớn
                gallery.style.position = 'sticky';
                gallery.style.top = '20px';

                quantityPanel.style.position = 'sticky';
                quantityPanel.style.top = '20px';
            } else {
                // Trở lại static cho màn hình nhỏ
                gallery.style.position = 'static';
                quantityPanel.style.position = 'static';
            }
        }

        // Áp dụng ngay khi tải trang
        handleStickyElements();

        // Áp dụng lại khi resize
        mediaQuery.addEventListener('change', handleStickyElements);

        // Xử lý tabs mô tả sản phẩm
        const tabButtons = document.querySelectorAll('.tab-btn');
        const descriptionContainer = document.querySelector('.product-description');
        const toggleButton = document.querySelector('.toggle-description');

        // Xử lý tabs điều hướng
        if (tabButtons.length > 0 && descriptionContainer && toggleButton) {
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Xóa active class từ tất cả các nút
                    tabButtons.forEach(btn => btn.classList.remove('active'));

                    // Thêm active class cho nút được click
                    this.classList.add('active');

                    // Lấy target ID từ data attribute
                    const targetId = this.getAttribute('data-target');

                    // Tìm heading tương ứng
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Mở rộng mô tả nếu đang thu gọn
                        if (descriptionContainer.classList.contains('collapsed')) {
                            descriptionContainer.classList.remove('collapsed');
                            descriptionContainer.classList.add('expanded');
                            toggleButton.textContent = 'Thu nhỏ';
                        }

                        // Scroll đến heading
                        setTimeout(() => {
                            const headerOffset = 80;
                            const elementPosition = targetElement.getBoundingClientRect().top;
                            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                            window.scrollTo({
                                top: offsetPosition,
                                behavior: 'smooth'
                            });
                        }, 100);
                    }
                });
            });

            // Xử lý nút toggle mô tả
            toggleButton.addEventListener('click', function() {
                if (descriptionContainer.classList.contains('collapsed')) {
                    // Mở rộng mô tả
                    descriptionContainer.classList.remove('collapsed');
                    descriptionContainer.classList.add('expanded');
                    this.textContent = 'Thu nhỏ';
                } else {
                    // Thu gọn mô tả
                    descriptionContainer.classList.remove('expanded');
                    descriptionContainer.classList.add('collapsed');
                    this.textContent = 'Xem thêm';

                    // Scroll lên đầu phần mô tả
                    const descriptionTabs = document.querySelector('.description-tabs');
                    if (descriptionTabs) {
                        const headerOffset = 20;
                        const elementPosition = descriptionTabs.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        }

        // Xử lý filter buttons cho Q&A
        const filterButtons = document.querySelectorAll('.filter-btn');

        if (filterButtons.length > 0) {
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Xóa active class từ tất cả các nút
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // Thêm active class cho nút được click
                    this.classList.add('active');

                    // Lấy filter type từ data attribute
                    const filterType = this.getAttribute('data-filter');

                    // Sắp xếp câu hỏi theo filterType
                    sortQuestions(filterType);
                });
            });
        }

        // Sắp xếp mặc định theo thứ tự cũ nhất khi trang load
        sortQuestions('newest');
    });

    // Hàm hiển thị form gửi câu hỏi
    function showQuestionForm() {
        const form = document.getElementById('question-form');
        if (form) {
            form.style.display = 'block';
            document.getElementById('question-text').focus();
        }
    }

    // Hàm ẩn form gửi câu hỏi
    function hideQuestionForm() {
        const form = document.getElementById('question-form');
        if (form) {
            form.style.display = 'none';
            document.getElementById('question-text').value = '';
        }
    }

    // Hàm gửi câu hỏi
    function submitQuestion() {
        const questionText = document.getElementById('question-text').value.trim();
        const productId = document.querySelector('[data-product-id]')?.dataset.productId;

        if (!questionText) {
            Toastify({
                text: "Vui lòng nhập câu hỏi của bạn",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
            return;
        }

        if (!productId) {
            Toastify({
                text: "Không tìm thấy thông tin sản phẩm",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
            return;
        }

        // Hiển thị loading
        const submitBtn = document.querySelector('.submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Đang gửi...';
        submitBtn.disabled = true;

        // Gọi API gửi câu hỏi
        fetch('/api/medical/product-questions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                product_id: productId,
                question: questionText,
                _token: '{{ csrf_token() }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hiển thị thông báo thành công
                Toastify({
                    text: "Câu hỏi của bạn đã được gửi thành công!",
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#059669",
                    close: true
                }).showToast();

                // Ẩn form và reset
                hideQuestionForm();

                // Reload trang để hiển thị câu hỏi mới
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                // Hiển thị thông báo lỗi
                Toastify({
                    text: data.message || "Có lỗi xảy ra khi gửi câu hỏi",
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#EF4444",
                    close: true
                }).showToast();

                // Nếu cần chuyển hướng đến trang đăng nhập
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Toastify({
                text: "Có lỗi xảy ra khi gửi câu hỏi. Vui lòng thử lại sau.",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
        })
        .finally(() => {
            // Khôi phục trạng thái nút
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        });
    }

    // Hàm sắp xếp câu hỏi
    function sortQuestions(sortType) {
        const qaContent = document.getElementById('qa-content');
        const qaItems = Array.from(qaContent.querySelectorAll('.qa-item'));

        if (qaItems.length === 0) return;

        // Lưu thứ tự gốc để dùng cho view_more
        if (!window.originalQaItems) {
            window.originalQaItems = qaItems.map(item => ({
                element: item.cloneNode(true),
                timestamp: parseInt(item.getAttribute('data-created'))
            }));
        }

        // Lưu thứ tự hiện tại sau khi sort để dùng cho view_more
        window.currentSortType = sortType;

        qaItems.sort((a, b) => {
            const timeA = parseInt(a.getAttribute('data-created'));
            const timeB = parseInt(b.getAttribute('data-created'));

            if (sortType === 'newest') {
                return timeB - timeA; // Mới nhất trước
            } else if (sortType === 'oldest') {
                return timeA - timeB; // Cũ nhất trước
            }
            return 0;
        });

        // Xóa tất cả items hiện tại
        qaItems.forEach(item => item.remove());

        // Thêm lại theo thứ tự mới
        qaItems.forEach(item => qaContent.appendChild(item));

        // Reset trạng thái view_more: hiển thị 3 item đầu tiên, ẩn phần còn lại
        qaItems.forEach((item, index) => {
            if (index < 3) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });

        // Reset nút view_more
        const viewMoreBtn = document.querySelector('.load-more-btn[data-parent="qa-content"]');
        if (viewMoreBtn) {
            const btnTextMore = viewMoreBtn.getAttribute('data-more');
            viewMoreBtn.innerText = btnTextMore;

            // Hiển thị/ẩn nút dựa trên số lượng items
            if (qaItems.length <= 3) {
                viewMoreBtn.style.display = 'none';
            } else {
                viewMoreBtn.style.display = '';
            }
        }

        // Override view_more behavior để hiển thị theo thứ tự cũ nhất
        setTimeout(() => {
            const viewMoreBtn = document.querySelector('.load-more-btn[data-parent="qa-content"]');
            if (viewMoreBtn) {
                // Remove existing event listeners
                const newBtn = viewMoreBtn.cloneNode(true);
                viewMoreBtn.parentNode.replaceChild(newBtn, viewMoreBtn);

                let qaShowing = 3;

                // Add custom event listener
                newBtn.addEventListener('click', function() {
                    if (newBtn.innerText === 'Xem thêm') {
                        // Hiển thị thêm items theo thứ tự filter hiện tại
                        if (window.originalQaItems) {
                            // Xóa tất cả items hiện tại
                            const currentItems = qaContent.querySelectorAll('.qa-item');
                            currentItems.forEach(item => item.remove());

                            // Tăng số lượng hiển thị
                            qaShowing += 3;

                            // Sắp xếp theo filter hiện tại
                            let sortedItems = [...window.originalQaItems];
                            if (window.currentSortType === 'newest') {
                                sortedItems.sort((a, b) => b.timestamp - a.timestamp); // Mới nhất trước
                            } else {
                                sortedItems.sort((a, b) => a.timestamp - b.timestamp); // Cũ nhất trước
                            }

                            // Thêm lại theo thứ tự đã sort với số lượng tăng thêm
                            sortedItems.forEach((itemData, index) => {
                                const newItem = itemData.element.cloneNode(true);
                                qaContent.appendChild(newItem);

                                // Hiển thị items theo số lượng qaShowing
                                if (index < qaShowing) {
                                    newItem.style.display = '';
                                } else {
                                    newItem.style.display = 'none';
                                }
                            });

                            if (qaShowing >= sortedItems.length) {
                                newBtn.innerText = 'Thu gọn';
                            }
                        }
                    } else {
                        // Thu gọn về 3 item đầu theo thứ tự filter hiện tại
                        if (window.originalQaItems) {
                            const currentItems = qaContent.querySelectorAll('.qa-item');
                            currentItems.forEach(item => item.remove());

                            // Sắp xếp theo filter hiện tại
                            let sortedItems = [...window.originalQaItems];
                            if (window.currentSortType === 'newest') {
                                sortedItems.sort((a, b) => b.timestamp - a.timestamp); // Mới nhất trước
                            } else {
                                sortedItems.sort((a, b) => a.timestamp - b.timestamp); // Cũ nhất trước
                            }

                            // Thêm lại theo thứ tự đã sort, chỉ hiển thị 3 item đầu
                            sortedItems.forEach((itemData, index) => {
                                const newItem = itemData.element.cloneNode(true);
                                qaContent.appendChild(newItem);

                                if (index < 3) {
                                    newItem.style.display = '';
                                } else {
                                    newItem.style.display = 'none';
                                }
                            });
                        }
                        qaShowing = 3;
                        newBtn.innerText = 'Xem thêm';
                    }
                });
            }
        }, 100);
    }

    // Xử lý chức năng thêm vào giỏ hàng - không sử dụng nữa
    function addToCart() {
        buyNow(); // Chuyển hướng sang hàm buyNow
    }

    // Xử lý chức năng mua ngay
    function buyNow() {
        const quantity = document.getElementById('quantity').value;
        const productId = document.querySelector('[data-product-id]')?.dataset.productId;

        if (!productId) {
            Toastify({
                text: "Không tìm thấy thông tin sản phẩm. Vui lòng thử lại sau.",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
            return;
        }

        // Hiển thị loading
        const buyNowBtn = document.querySelector('.buy-now-btn');
        const originalText = buyNowBtn.textContent;
        buyNowBtn.textContent = 'Đang xử lý...';
        buyNowBtn.disabled = true;

        // Gọi API thêm vào giỏ hàng
        fetch('/api/medical/cart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity,
                _token: '{{ csrf_token() }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hiển thị thông báo thành công
                Toastify({
                    text: "Đã thêm sản phẩm vào giỏ hàng",
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#059669",
                    close: true
                }).showToast();

                // Cập nhật số lượng sản phẩm trong giỏ hàng (nếu có)
                if (data.cart_count) {
                    const cartCountElement = document.querySelector('.cart-count');
                    if (cartCountElement) {
                        cartCountElement.textContent = data.cart_count;
                    }
                }
            } else {
                // Hiển thị thông báo lỗi
                Toastify({
                    text: data.message,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#EF4444",
                    close: true
                }).showToast();

                // Nếu cần chuyển hướng đến trang đăng nhập
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Toastify({
                text: "Có lỗi xảy ra khi thêm vào giỏ hàng. Vui lòng thử lại sau.",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
        })
        .finally(() => {
            // Khôi phục trạng thái nút
            buyNowBtn.textContent = originalText;
            buyNowBtn.disabled = false;
        });
    }

    // Xử lý chức năng thêm vào báo giá
    function addToQuote() {
        const quantity = document.getElementById('quantity').value;
        const productId = document.querySelector('[data-product-id]')?.dataset.productId;

        if (!productId) {
            Toastify({
                text: "Không tìm thấy thông tin sản phẩm. Vui lòng thử lại sau.",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
            return;
        }

        // Hiển thị loading
        const quoteBtn = document.querySelector('.quote-btn');
        const originalText = quoteBtn.textContent;
        quoteBtn.textContent = 'Đang xử lý...';
        quoteBtn.disabled = true;
        const activeBtn = document.querySelector('.category-btn.active');
        const unitType = activeBtn ? activeBtn.getAttribute('data-unit-type') : null;

        // Gọi API thêm vào báo giá
        fetch('/api/medical/quote', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity,
                unit_type: unitType,
                _token: '{{ csrf_token() }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hiển thị thông báo thành công
                Toastify({
                    text: data.message,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#059669",
                    close: true
                }).showToast();

                // Cập nhật số lượng sản phẩm trong báo giá (nếu có)
                if (data.quote_count) {
                    const quoteCountElement = document.querySelector('.quote-count');
                    if (quoteCountElement) {
                        quoteCountElement.textContent = data.quote_count;
                    }
                }
            } else {
                // Hiển thị thông báo lỗi
                Toastify({
                    text: data.message,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#EF4444",
                    close: true
                }).showToast();

                // Nếu cần chuyển hướng đến trang đăng nhập
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Toastify({
                text: "Có lỗi xảy ra khi thêm vào báo giá. Vui lòng thử lại sau.",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
        })
        .finally(() => {
            // Khôi phục trạng thái nút
            quoteBtn.textContent = originalText;
            quoteBtn.disabled = false;
        });
    }

</script>

<!-- Include product card JavaScript -->
@vite(['resources/themes/medical/js/product-card.js'])

<script>
    // Xử lý sự kiện cho product cards trong phần "Sản phẩm cùng thương hiệu"
    document.addEventListener('DOMContentLoaded', function() {
        // Xử lý click vào product card để chuyển hướng
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach(card => {
            card.addEventListener('click', function(event) {
                // Nếu click vào button thì không xử lý chuyển hướng
                if (event.target.classList.contains('quick-order-add-btn') ||
                    event.target.closest('.quick-order-add-btn')) {
                    return;
                }

                // Lấy URL từ onclick attribute
                const onclickAttr = this.getAttribute('onclick');
                if (onclickAttr) {
                    // Extract URL from onclick="window.location.href='...'"
                    const urlMatch = onclickAttr.match(/window\.location\.href='([^']+)'/);
                    if (urlMatch && urlMatch[1]) {
                        window.location.href = urlMatch[1];
                    }
                }
            });
        });
    });
</script>

<style>
    /* Q&A Section Styles */
    .qa-section {
        margin-top: 2rem;
    }

    .qa-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .qa-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1f2937;
    }

    .qa-count {
        color: #6b7280;
    }

    .ask-question-btn {
        background-color: #f97316;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.2s;
    }

    .ask-question-btn:hover {
        background-color: #ea580c;
    }

    /* Question Form Styles */
    .question-form {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .cancel-btn {
        background-color: #6b7280;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        border: none;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.2s;
    }

    .cancel-btn:hover {
        background-color: #4b5563;
    }

    .submit-btn {
        background-color: #059669;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        border: none;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.2s;
    }

    .submit-btn:hover {
        background-color: #047857;
    }

    .submit-btn:disabled {
        background-color: #9ca3af;
        cursor: not-allowed;
    }

    /* Q&A Filters */
    .qa-filters {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }

    .filter-btn {
        background: none;
        border: none;
        padding: 0.5rem 1rem;
        cursor: pointer;
        color: #6b7280;
        font-weight: 500;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }

    .filter-btn:hover {
        background-color: #f3f4f6;
        color: #374151;
    }

    .filter-btn.active {
        background-color: #f97316;
        color: white;
    }

    /* Q&A Content */
    .qa-content {
        max-height: none;
        overflow: visible;
    }

    .qa-item {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        background-color: white;
    }

    .user-question {
        margin-bottom: 1rem;
    }

    .user-info, .admin-info {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .user-avatar, .admin-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 0.75rem;
    }

    .user-avatar img, .admin-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .user-name-badge, .admin-name-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .user-name, .admin-name {
        font-weight: 600;
        color: #1f2937;
    }

    .question-time, .answer-time {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .question-text {
        color: #374151;
        line-height: 1.5;
        margin-top: 0.5rem;
    }

    .admin-answer {
        background-color: #f0f9ff;
        border: 1px solid #e0f2fe;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    .answer-text {
        color: #374151;
        line-height: 1.5;
        margin-top: 0.5rem;
    }

    .answer-text p {
        margin-bottom: 0.5rem;
    }

    .verified-badge {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-color: #059669;
        border-radius: 50%;
        position: relative;
    }

    .verified-badge::after {
        content: '✓';
        color: white;
        font-size: 10px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .no-questions {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
        font-style: italic;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .qa-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .qa-filters {
            flex-wrap: wrap;
        }

        .user-info, .admin-info {
            flex-direction: column;
            align-items: flex-start;
        }

        .user-avatar, .admin-avatar {
            margin-right: 0;
            margin-bottom: 0.5rem;
        }
    }
</style>

@endsection