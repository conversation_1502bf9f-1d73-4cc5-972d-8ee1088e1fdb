<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Medical Shop</title>

    <!-- Sử dụng Tailwind từ CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Thêm font Be Vietnam Pro -->
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Thêm Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Thêm vào phần head -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <?php echo app('App\Services\MedicalViteService')->renderAssets([
        'resources/themes/medical/css/app.css',
        'resources/themes/medical/css/header.css',
        'resources/themes/medical/css/footer.css',
        'resources/themes/medical/css/input-focus.css'
    ]); ?>

    <?php $__env->startPush('styles'); ?>
    <style>
        body {
            font-family: 'Be Vietnam Pro', sans-serif;
        }
    </style>
</head>
<body class="bg-white <?php echo e(Auth::guard('customer')->check() ? 'customer-logged-in' : ''); ?>">
    <?php echo $__env->make('medical::layouts.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Main content -->
    <main class="container mx-auto p-4">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <?php echo $__env->make('medical::layouts.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Thêm vào cuối file, trước thẻ đóng body -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- Vite JS -->
    <?php echo app('App\Services\MedicalViteService')->renderAssets('resources/themes/medical/js/app.js'); ?>

    <!-- Cập nhật số lượng báo giá -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Chỉ cập nhật nếu người dùng đã đăng nhập
            if (document.body.classList.contains('customer-logged-in')) {
                updateQuoteCount();
            }
        });

        // Hàm cập nhật số lượng báo giá - Global function
        function updateQuoteCount() {
            fetch('/api/medical/quote/count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const quoteCountElement = document.getElementById('header-quote-count');
                        if (quoteCountElement) {
                            quoteCountElement.textContent = data.quote_count;

                            // Hiển thị/ẩn số lượng dựa trên giá trị
                            if (data.quote_count > 0) {
                                quoteCountElement.style.display = 'flex';
                                quoteCountElement.style.justifyContent = 'center';
                                quoteCountElement.style.alignItems = 'center';
                            } else {
                                quoteCountElement.style.display = 'none';
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating quote count:', error);
                });
        }

        // Hàm cập nhật header quote count - có thể gọi từ bất kỳ đâu
        function updateHeaderQuoteCount() {
            updateQuoteCount();
        }
    </script>

    <!-- Bootstrap JS (bundle includes Popper) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html><?php /**PATH /var/www/html/app/Providers/../../resources/themes/medical/views/layouts/app.blade.php ENDPATH**/ ?>